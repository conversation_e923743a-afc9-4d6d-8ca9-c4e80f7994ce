# Resemble.ai Voice Cloning Web App

A professional web application for voice cloning using the Resemble.ai API with a Node.js backend to handle CORS restrictions.

## Features

- **Project Management**: Organize voice models into projects
- **Voice Cloning**: Create custom voice models from audio samples
- **Real-time Speech**: Generate speech with cloned voices
- **Recording**: Record audio directly in the browser
- **Voice Management**: View, select, and delete voice models
- **Professional UI**: Modern, responsive interface with workflow guidance

## Setup Instructions

### Prerequisites

1. **Node.js**: Download and install from [nodejs.org](https://nodejs.org/)
2. **Resemble.ai Account**: Sign up at [resemble.ai](https://resemble.ai/)
3. **API Key**: Get your API key from [Resemble.ai Account Settings](https://app.resemble.ai/account)

### Installation

1. **Install dependencies:**
   ```bash
   npm install express cors multer form-data node-fetch nodemon
   ```
   
   Or run the setup script:
   ```bash
   setup_resemble.bat
   ```

2. **Configure API Key:**
   - Open `resemble_server.js`
   - Replace `'your_resemble_api_key_here'` with your actual Resemble.ai API key
   - Save the file

3. **Start the server:**
   ```bash
   node resemble_server.js
   ```

4. **Open your browser:**
   Navigate to `http://localhost:3002`

## How to Use

### 1. Create a Project
1. Enter a project name and description
2. Click "Create Project"
3. Select the project from the list

### 2. Create Voice Models
1. Select a project
2. Enter a voice name
3. Upload an audio file or record directly
4. Click "Create Voice"
5. Wait for processing (1-2 minutes)

### 3. Generate Speech
1. Select a voice from your project
2. Enter the text you want to convert to speech
3. Add an optional clip title
4. Click "Generate Speech"
5. Download the generated audio

## API Endpoints

The server provides these endpoints:

- `GET /api/projects` - Get all projects
- `POST /api/projects` - Create a new project
- `GET /api/projects/:id/voices` - Get voices in a project
- `POST /api/projects/:id/voices` - Create a voice in a project
- `POST /api/projects/:id/clips` - Generate speech
- `GET /api/projects/:id/clips/:clip_id` - Get clip status
- `DELETE /api/projects/:id/voices/:voice_id` - Delete a voice
- `DELETE /api/projects/:id` - Delete a project

## Voice Cloning Best Practices

### Audio Requirements
- **Duration**: 10 seconds to 10 minutes of audio
- **Quality**: Clear, high-quality recordings (16kHz+ sample rate)
- **Format**: WAV, MP3, FLAC, or other common formats
- **Content**: Natural speech, avoid background noise
- **Single Speaker**: Only one person speaking

### Recording Tips
1. **Quiet Environment**: Record in a noise-free space
2. **Consistent Tone**: Use natural, conversational speech
3. **Good Microphone**: Use quality recording equipment
4. **Avoid Compression**: Don't over-compress the audio
5. **Clear Pronunciation**: Speak clearly and at normal pace

## Workflow

```
1. Create Project → 2. Upload Audio → 3. Create Voice → 4. Generate Speech
```

### Project Organization
- Group related voices into projects
- Use descriptive project names
- Add descriptions for better organization

### Voice Model Creation
- Each voice requires one audio sample
- Processing takes 1-2 minutes
- Voice quality depends on input audio quality

### Speech Generation
- Real-time synthesis (usually < 10 seconds)
- Supports various text lengths
- High-quality MP3 output

## Troubleshooting

### API Key Issues
- Verify your API key is correct
- Check your Resemble.ai account is active
- Ensure you have sufficient credits

### Voice Creation Fails
- Check audio file quality and format
- Ensure file size is reasonable (< 100MB)
- Try with different audio samples
- Verify the audio contains clear speech

### Speech Generation Issues
- Ensure voice model creation completed successfully
- Check that the voice is selected
- Verify text input is not empty

### Network Errors
- Verify server is running on port 3002
- Check firewall settings
- Ensure stable internet connection

## File Structure

```
├── resemble_server.js              # Node.js backend server
├── resemble_voice_cloning.html     # Web interface
├── setup_resemble.bat              # Setup script
└── README_Resemble.md              # This file
```

## Development

For development with auto-restart:
```bash
nodemon resemble_server.js
```

## Production Deployment

1. Set API key as environment variable
2. Use HTTPS for secure microphone access
3. Configure proper CORS for your domain
4. Use process manager like PM2
5. Set up proper logging and monitoring

## Resemble.ai Features

### Voice Cloning
- High-quality voice synthesis
- Fast model training (1-2 minutes)
- Real-time speech generation
- Multiple voice support per project

### API Capabilities
- RESTful API design
- Project-based organization
- Clip management and status tracking
- High-quality audio output

### Pricing Tiers
- **Free**: Limited usage for testing
- **Basic**: $0.006 per second of generated audio
- **Pro**: Volume discounts and additional features
- **Enterprise**: Custom pricing and features

Check your usage and billing at [app.resemble.ai](https://app.resemble.ai/)

## Support

- Resemble.ai Documentation: [docs.resemble.ai](https://docs.resemble.ai/)
- API Reference: [docs.resemble.ai/reference](https://docs.resemble.ai/reference)
- Support: [resemble.ai/contact](https://resemble.ai/contact)

## Comparison with Other Services

| Feature | Resemble.ai | ElevenLabs | Fish Audio |
|---------|-------------|------------|------------|
| **Setup Speed** | ⭐⭐⭐⭐⭐ Very Fast | ⭐⭐⭐⭐ Fast | ⭐⭐⭐ Moderate |
| **Voice Quality** | ⭐⭐⭐⭐ Excellent | ⭐⭐⭐⭐⭐ Outstanding | ⭐⭐⭐⭐ Very Good |
| **Project Organization** | ⭐⭐⭐⭐⭐ Excellent | ⭐⭐⭐ Good | ⭐⭐ Basic |
| **Real-time Generation** | ⭐⭐⭐⭐⭐ Excellent | ⭐⭐⭐⭐ Very Good | ⭐⭐⭐ Good |
| **API Design** | ⭐⭐⭐⭐⭐ Excellent | ⭐⭐⭐⭐ Very Good | ⭐⭐⭐ Good |
| **Documentation** | ⭐⭐⭐⭐ Very Good | ⭐⭐⭐⭐⭐ Excellent | ⭐⭐⭐ Good |

Resemble.ai excels in project organization and real-time synthesis speed, making it ideal for applications requiring multiple voice models and fast generation.
