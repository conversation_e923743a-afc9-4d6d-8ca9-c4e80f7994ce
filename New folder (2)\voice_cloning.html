<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fish Audio Voice Cloning App</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
        }

        .header h1 {
            color: #2d3748;
            font-size: 2.5rem;
            margin-bottom: 10px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .header p {
            color: #718096;
            font-size: 1.1rem;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }

        .section {
            background: #f7fafc;
            border-radius: 15px;
            padding: 25px;
            border: 2px solid #e2e8f0;
            transition: all 0.3s ease;
        }

        .section:hover {
            border-color: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.1);
        }

        .section h2 {
            color: #2d3748;
            margin-bottom: 20px;
            font-size: 1.5rem;
            display: flex;
            align-items: center;
        }

        .section h2::before {
            content: '🎤';
            margin-right: 10px;
            font-size: 1.2rem;
        }

        .cloning-section h2::before {
            content: '🔄';
        }

        .input-options {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .file-input-wrapper {
            flex: 1;
            min-width: 200px;
        }

        .record-wrapper {
            flex: 1;
            min-width: 200px;
        }

        .btn-record {
            width: 100%;
            padding: 15px 20px;
            border: 2px solid #667eea;
            background: transparent;
            color: #667eea;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
            font-size: 1rem;
        }

        .btn-record:hover {
            background: #667eea;
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }

        .btn-record.recording {
            background: #e53e3e;
            color: white;
            border-color: #e53e3e;
            animation: pulse 1.5s infinite;
        }

        .btn-record.recording:hover {
            background: #c53030;
            border-color: #c53030;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .record-status {
            margin-top: 10px;
            text-align: center;
            font-size: 0.9rem;
            color: #718096;
        }

        .record-status.recording {
            color: #e53e3e;
            font-weight: 500;
        }

        .record-timer {
            font-family: 'Courier New', monospace;
            font-weight: bold;
            color: #e53e3e;
        }

        .file-input {
            display: none;
        }

        .file-input-label {
            display: block;
            padding: 15px 20px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-radius: 10px;
            cursor: pointer;
            text-align: center;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .file-input-label:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }

        .file-status {
            margin-top: 10px;
            padding: 10px;
            border-radius: 8px;
            font-size: 0.9rem;
        }

        .file-status.success {
            background: #c6f6d5;
            color: #2f855a;
            border: 1px solid #9ae6b4;
        }

        .file-status.error {
            background: #fed7d7;
            color: #c53030;
            border: 1px solid #feb2b2;
        }

        .audio-player {
            width: 100%;
            margin: 15px 0;
            border-radius: 8px;
        }

        .controls {
            display: flex;
            gap: 15px;
            margin-top: 20px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
            font-size: 1rem;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .btn-secondary {
            background: #e2e8f0;
            color: #4a5568;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .text-input {
            width: 100%;
            padding: 15px;
            border: 2px solid #e2e8f0;
            border-radius: 10px;
            font-size: 1rem;
            margin-bottom: 20px;
            transition: border-color 0.3s ease;
        }

        .text-input:focus {
            outline: none;
            border-color: #667eea;
        }

        .output-section {
            grid-column: 1 / -1;
            background: #f7fafc;
            border-radius: 15px;
            padding: 25px;
            border: 2px solid #e2e8f0;
        }

        .output-section h2 {
            color: #2d3748;
            margin-bottom: 20px;
            font-size: 1.5rem;
        }

        .output-section h2::before {
            content: '🎵';
            margin-right: 10px;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }

        .loading.show {
            display: block;
        }

        .spinner {
            border: 4px solid #e2e8f0;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .result-audio {
            margin-top: 20px;
        }

        .error-message {
            background: #fed7d7;
            color: #c53030;
            padding: 15px;
            border-radius: 8px;
            margin-top: 15px;
            border: 1px solid #feb2b2;
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .controls {
                flex-direction: column;
            }
            
            .input-options {
                flex-direction: column;
            }
            
            .file-input-wrapper,
            .record-wrapper {
                min-width: unset;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Fish Audio Voice Cloning</h1>
            <p>Upload a reference voice and clone other voices with its tone characteristics</p>
        </div>

        <div class="main-content">
            <div class="section reference-section">
                <h2>Reference Voice</h2>
                <div class="input-options">
                    <div class="file-input-wrapper">
                        <input type="file" id="referenceFile" class="file-input" accept="audio/*">
                        <label for="referenceFile" class="file-input-label">
                            📁 Upload Reference Voice
                        </label>
                    </div>
                    <div class="record-wrapper">
                        <button id="referenceRecordBtn" class="btn-record">
                            🎙️ Record Voice
                        </button>
                        <div id="referenceRecordStatus" class="record-status"></div>
                    </div>
                </div>
                <div id="referenceStatus" class="file-status" style="display: none;"></div>
                <audio id="referencePlayer" class="audio-player" controls style="display: none;"></audio>
                <div class="controls">
                    <button id="analyzeBtn" class="btn btn-primary" disabled>
                        🔍 Analyze Voice
                    </button>
                </div>
            </div>

            <div class="section cloning-section">
                <h2>Voice to Clone</h2>
                <div class="input-options">
                    <div class="file-input-wrapper">
                        <input type="file" id="targetFile" class="file-input" accept="audio/*">
                        <label for="targetFile" class="file-input-label">
                            📁 Upload Voice to Clone
                        </label>
                    </div>
                    <div class="record-wrapper">
                        <button id="targetRecordBtn" class="btn-record">
                            🎙️ Record Voice
                        </button>
                        <div id="targetRecordStatus" class="record-status"></div>
                    </div>
                </div>
                <div id="targetStatus" class="file-status" style="display: none;"></div>
                <audio id="targetPlayer" class="audio-player" controls style="display: none;"></audio>
                
                <textarea 
                    id="textInput" 
                    class="text-input" 
                    placeholder="Enter the text you want the cloned voice to speak..."
                    rows="4"
                ></textarea>
                
                <div class="controls">
                    <button id="cloneBtn" class="btn btn-primary" disabled>
                        🔄 Clone Voice
                    </button>
                    <button id="clearBtn" class="btn btn-secondary">
                        🗑️ Clear All
                    </button>
                </div>
            </div>
        </div>

        <div class="output-section">
            <h2>Generated Audio</h2>
            <div id="loading" class="loading">
                <div class="spinner"></div>
                <p>Processing voice cloning... This may take a few moments.</p>
            </div>
            <div id="outputContainer" style="display: none;">
                <audio id="outputPlayer" class="audio-player" controls></audio>
                <div class="controls">
                    <button id="downloadBtn" class="btn btn-primary">
                        ⬇️ Download Audio
                    </button>
                </div>
            </div>
            <div id="errorContainer" style="display: none;">
                <div class="error-message" id="errorMessage"></div>
            </div>
        </div>
    </div>

    <script>
        const API_KEY = '********************************';
        const API_BASE_URL = 'https://api.fish.audio';
        
        // Correct Fish Audio API endpoints based on documentation
        const FISH_API_ENDPOINTS = {
            models: '/v1/models',
            tts: '/v1/tts',
            stt: '/v1/stt',
            credit: '/v1/wallet/credit'
        };
        
        let referenceVoiceData = null;
        let targetVoiceData = null;
        let referenceVoiceId = null;
        
        // Recording variables
        let mediaRecorder = null;
        let recordedChunks = [];
        let currentRecordingType = null;
        let recordingTimer = null;
        let recordingStartTime = null;

        // File handling
        document.getElementById('referenceFile').addEventListener('change', handleReferenceFile);
        document.getElementById('targetFile').addEventListener('change', handleTargetFile);
        
        // Recording handlers
        document.getElementById('referenceRecordBtn').addEventListener('click', () => toggleRecording('reference'));
        document.getElementById('targetRecordBtn').addEventListener('click', () => toggleRecording('target'));
        
        // Button handlers
        document.getElementById('analyzeBtn').addEventListener('click', analyzeReferenceVoice);
        document.getElementById('cloneBtn').addEventListener('click', cloneVoice);
        document.getElementById('clearBtn').addEventListener('click', clearAll);
        document.getElementById('downloadBtn').addEventListener('click', downloadAudio);

        function handleReferenceFile(event) {
            const file = event.target.files[0];
            if (file) {
                referenceVoiceData = file;
                showFileStatus('referenceStatus', `✅ ${file.name} loaded`, 'success');
                
                const url = URL.createObjectURL(file);
                const player = document.getElementById('referencePlayer');
                player.src = url;
                player.style.display = 'block';
                
                document.getElementById('analyzeBtn').disabled = false;
            }
        }

        function handleTargetFile(event) {
            const file = event.target.files[0];
            if (file) {
                targetVoiceData = file;
                showFileStatus('targetStatus', `✅ ${file.name} loaded`, 'success');
                
                const url = URL.createObjectURL(file);
                const player = document.getElementById('targetPlayer');
                player.src = url;
                player.style.display = 'block';
                
                updateCloneButton();
            }
        }

        function showFileStatus(elementId, message, type) {
            const statusEl = document.getElementById(elementId);
            statusEl.textContent = message;
            statusEl.className = `file-status ${type}`;
            statusEl.style.display = 'block';
        }

        function updateCloneButton() {
            const textInput = document.getElementById('textInput').value.trim();
            const hasReference = referenceVoiceId !== null;
            const hasTarget = targetVoiceData !== null;
            const hasText = textInput.length > 0;
            
            document.getElementById('cloneBtn').disabled = !(hasReference && hasTarget && hasText);
        }

        document.getElementById('textInput').addEventListener('input', updateCloneButton);

        async function analyzeReferenceVoice() {
            if (!referenceVoiceData) return;

            try {
                showLoading();
                hideOutput();
                hideError();

                console.log('Creating reference voice model...');
                
                // Use the correct Fish Audio API format
                const formData = new FormData();
                formData.append('audio', referenceVoiceData, 'reference.wav');
                formData.append('name', `Reference Voice ${Date.now()}`);
                formData.append('description', 'Reference voice for tone cloning');
                formData.append('type', 'tts'); // Specify model type

                const response = await fetch(`${API_BASE_URL}${FISH_API_ENDPOINTS.models}`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${API_KEY}`,
                        // Don't set Content-Type for FormData - browser will set it with boundary
                    },
                    body: formData
                });

                console.log(`API Response Status: ${response.status}`);
                
                if (response.ok) {
                    const result = await response.json();
                    console.log('Model creation response:', result);
                    referenceVoiceId = result.id || result._id;
                    
                    hideLoading();
                    showFileStatus('referenceStatus', '✅ Reference voice analyzed successfully!', 'success');
                    updateCloneButton();
                } else {
                    // Get detailed error message
                    const errorText = await response.text();
                    console.log('API Error Response:', errorText);
                    
                    let errorMessage = `API Error ${response.status}: `;
                    try {
                        const errorJson = JSON.parse(errorText);
                        errorMessage += errorJson.message || errorJson.error || errorText;
                    } catch {
                        errorMessage += errorText;
                    }
                    
                    if (response.status === 401) {
                        errorMessage = 'Authentication failed. Please check your API key.';
                    } else if (response.status === 403) {
                        errorMessage = 'Access forbidden. Please check your API permissions or credit balance.';
                    } else if (response.status === 429) {
                        errorMessage = 'Rate limit exceeded. Please wait before trying again.';
                    }
                    
                    throw new Error(errorMessage);
                }

            } catch (error) {
                console.error('Error analyzing reference voice:', error);
                hideLoading();
                
                if (error.name === 'TypeError' && error.message.includes('fetch')) {
                    // Network/CORS error
                    showError(`Connection Error: Cannot reach Fish Audio API. This could be due to:
                    
• CORS restrictions (API calls from browser may be blocked)
• Network connectivity issues  
• Incorrect API base URL
• Firewall/proxy blocking the request

For production use, consider implementing this on a backend server that can make direct API calls to Fish Audio.

The app will now switch to demo mode.`);
                    
                    // Enable demo mode
                    referenceVoiceId = 'demo_reference_voice';
                    showFileStatus('referenceStatus', '⚠️ Demo mode enabled - Reference voice ready', 'success');
                    updateCloneButton();
                } else {
                    // API-specific error
                    showError(`API Error: ${error.message}

Please check:
• Your API key is valid and active
• You have sufficient API credits
• The audio file format is supported
• Your account has the necessary permissions

The app will switch to demo mode for testing the interface.`);
                    
                    // Enable demo mode as fallback
                    referenceVoiceId = 'demo_reference_voice';
                    showFileStatus('referenceStatus', '⚠️ Demo mode enabled - Reference voice ready', 'success');
                    updateCloneButton();
                }
            }
        }

        async function cloneVoice() {
            const textInput = document.getElementById('textInput').value.trim();
            
            if (!referenceVoiceId || !targetVoiceData || !textInput) {
                showError('Please ensure you have uploaded both audio files, analyzed the reference voice, and entered text.');
                return;
            }

            try {
                showLoading();
                hideOutput();
                hideError();

                // Handle demo mode
                if (referenceVoiceId.includes('demo')) {
                    console.log('Running in demo mode...');
                    await new Promise(resolve => setTimeout(resolve, 2000));
                    const demoAudio = await createDemoAudio(textInput);
                    hideLoading();
                    showOutput(demoAudio.url, demoAudio.blob);
                    showError('Demo Mode: This is a simulated result using browser text-to-speech. In production, this would use Fish Audio API for real voice cloning.');
                    return;
                }

                console.log('Creating target voice model...');
                
                // First, create a model from the target voice
                const targetFormData = new FormData();
                targetFormData.append('audio', targetVoiceData, 'target.wav');
                targetFormData.append('name', `Target Voice ${Date.now()}`);
                targetFormData.append('description', 'Target voice for cloning');
                targetFormData.append('type', 'tts');

                const targetResponse = await fetch(`${API_BASE_URL}${FISH_API_ENDPOINTS.models}`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${API_KEY}`,
                    },
                    body: targetFormData
                });

                let targetVoiceId;
                if (targetResponse.ok) {
                    const targetResult = await targetResponse.json();
                    targetVoiceId = targetResult.id || targetResult._id;
                    console.log('Target voice model created:', targetVoiceId);
                } else {
                    const errorText = await targetResponse.text();
                    console.log('Target voice creation failed:', errorText);
                    throw new Error(`Failed to create target voice model: ${targetResponse.status}`);
                }

                console.log('Generating speech with voice cloning...');
                
                // Now perform TTS with voice cloning
                const ttsPayload = {
                    text: textInput,
                    reference_id: targetVoiceId, // Voice to clone
                    format: 'mp3',
                    latency: 'normal'
                };

                // If we have a reference voice for tone, include it
                if (referenceVoiceId && !referenceVoiceId.includes('demo')) {
                    ttsPayload.emotion_reference_id = referenceVoiceId;
                }

                const ttsResponse = await fetch(`${API_BASE_URL}${FISH_API_ENDPOINTS.tts}`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${API_KEY}`,
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(ttsPayload)
                });

                if (ttsResponse.ok) {
                    const audioBlob = await ttsResponse.blob();
                    const audioUrl = URL.createObjectURL(audioBlob);
                    
                    hideLoading();
                    showOutput(audioUrl, audioBlob);
                    console.log('Voice cloning successful!');
                } else {
                    const errorText = await ttsResponse.text();
                    console.log('TTS failed:', errorText);
                    
                    let errorMessage = `TTS Error ${ttsResponse.status}: `;
                    try {
                        const errorJson = JSON.parse(errorText);
                        errorMessage += errorJson.message || errorJson.error || errorText;
                    } catch {
                        errorMessage += errorText;
                    }
                    
                    throw new Error(errorMessage);
                }

            } catch (error) {
                console.error('Error cloning voice:', error);
                hideLoading();
                
                // Provide fallback demo audio
                try {
                    console.log('Falling back to demo mode...');
                    const demoAudio = await createDemoAudio(textInput);
                    showOutput(demoAudio.url, demoAudio.blob);
                    
                    if (error.message.includes('fetch')) {
                        showError(`Connection Error: ${error.message}

This appears to be a network/CORS issue. The voice cloning API cannot be accessed directly from the browser.

Showing demo result using browser text-to-speech instead.

For production: Implement Fish Audio API calls on a backend server.`);
                    } else {
                        showError(`API Error: ${error.message}

Please check your API key, credits, and account permissions.

Showing demo result for interface testing.`);
                    }
                } catch (demoError) {
                    showError(`Voice cloning failed: ${error.message}

Could not create demo audio: ${demoError.message}`);
                }
            }
        }

        // Create a simple demo audio using Web Speech API or text-to-speech
        async function createDemoAudio(text) {
            return new Promise((resolve, reject) => {
                try {
                    // Use Web Speech API if available
                    if ('speechSynthesis' in window) {
                        const utterance = new SpeechSynthesisUtterance(text);
                        utterance.rate = 0.9;
                        utterance.pitch = 1;
                        utterance.volume = 1;
                        
                        // Try to use a natural voice
                        const voices = speechSynthesis.getVoices();
                        const preferredVoice = voices.find(voice => 
                            voice.name.includes('Natural') || 
                            voice.name.includes('Google') ||
                            voice.lang.startsWith('en')
                        );
                        if (preferredVoice) {
                            utterance.voice = preferredVoice;
                        }

                        // Create audio context to capture the speech
                        const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                        const destination = audioContext.createMediaStreamDestination();
                        const mediaRecorder = new MediaRecorder(destination.stream);
                        const chunks = [];

                        mediaRecorder.ondataavailable = (event) => {
                            chunks.push(event.data);
                        };

                        mediaRecorder.onstop = () => {
                            const blob = new Blob(chunks, { type: 'audio/webm' });
                            const url = URL.createObjectURL(blob);
                            resolve({ url, blob });
                        };

                        utterance.onstart = () => {
                            mediaRecorder.start();
                        };

                        utterance.onend = () => {
                            setTimeout(() => {
                                mediaRecorder.stop();
                            }, 500);
                        };

                        speechSynthesis.speak(utterance);
                    } else {
                        // Fallback: create a simple tone as demo
                        const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                        const duration = Math.min(text.length * 0.1, 5); // Max 5 seconds
                        const sampleRate = audioContext.sampleRate;
                        const numSamples = duration * sampleRate;
                        const audioBuffer = audioContext.createBuffer(1, numSamples, sampleRate);
                        const channelData = audioBuffer.getChannelData(0);

                        // Generate a simple tone pattern
                        for (let i = 0; i < numSamples; i++) {
                            const t = i / sampleRate;
                            channelData[i] = Math.sin(2 * Math.PI * 440 * t) * 0.1 * Math.exp(-t);
                        }

                        // Convert to blob
                        const wavBuffer = audioBufferToWav(audioBuffer);
                        const blob = new Blob([wavBuffer], { type: 'audio/wav' });
                        const url = URL.createObjectURL(blob);
                        resolve({ url, blob });
                    }
                } catch (error) {
                    reject(error);
                }
            });
        }

        function showLoading() {
            document.getElementById('loading').classList.add('show');
        }

        function hideLoading() {
            document.getElementById('loading').classList.remove('show');
        }

        function showOutput(audioUrl, audioBlob) {
            const outputPlayer = document.getElementById('outputPlayer');
            outputPlayer.src = audioUrl;
            outputPlayer.audioBlob = audioBlob; // Store for download
            document.getElementById('outputContainer').style.display = 'block';
        }

        function hideOutput() {
            document.getElementById('outputContainer').style.display = 'none';
        }

        function showError(message) {
            document.getElementById('errorMessage').textContent = message;
            document.getElementById('errorContainer').style.display = 'block';
        }

        function hideError() {
            document.getElementById('errorContainer').style.display = 'none';
        }

        function downloadAudio() {
            const outputPlayer = document.getElementById('outputPlayer');
            if (outputPlayer.audioBlob) {
                const url = URL.createObjectURL(outputPlayer.audioBlob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `cloned_voice_${Date.now()}.mp3`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
            }
        }

        // Recording functions
        async function toggleRecording(type) {
            if (mediaRecorder && mediaRecorder.state === 'recording') {
                stopRecording();
            } else {
                startRecording(type);
            }
        }

        async function startRecording(type) {
            try {
                const stream = await navigator.mediaDevices.getUserMedia({ 
                    audio: {
                        echoCancellation: true,
                        noiseSuppression: true,
                        sampleRate: 44100
                    } 
                });
                
                mediaRecorder = new MediaRecorder(stream, {
                    mimeType: 'audio/webm;codecs=opus'
                });
                
                recordedChunks = [];
                currentRecordingType = type;
                recordingStartTime = Date.now();
                
                mediaRecorder.ondataavailable = (event) => {
                    if (event.data.size > 0) {
                        recordedChunks.push(event.data);
                    }
                };
                
                mediaRecorder.onstop = () => {
                    const blob = new Blob(recordedChunks, { type: 'audio/webm' });
                    handleRecordedAudio(blob, currentRecordingType);
                    
                    // Stop all tracks to release microphone
                    stream.getTracks().forEach(track => track.stop());
                };
                
                mediaRecorder.start();
                updateRecordingUI(type, true);
                startTimer(type);
                
            } catch (error) {
                console.error('Error accessing microphone:', error);
                showError('Could not access microphone. Please check permissions.');
            }
        }

        function stopRecording() {
            if (mediaRecorder && mediaRecorder.state === 'recording') {
                mediaRecorder.stop();
                updateRecordingUI(currentRecordingType, false);
                stopTimer();
            }
        }

        function updateRecordingUI(type, isRecording) {
            const button = document.getElementById(`${type}RecordBtn`);
            const status = document.getElementById(`${type}RecordStatus`);
            
            if (isRecording) {
                button.textContent = '⏹️ Stop Recording';
                button.classList.add('recording');
                status.textContent = 'Recording... Click to stop';
                status.classList.add('recording');
            } else {
                button.textContent = '🎙️ Record Voice';
                button.classList.remove('recording');
                status.textContent = '';
                status.classList.remove('recording');
            }
        }

        function startTimer(type) {
            const status = document.getElementById(`${type}RecordStatus`);
            recordingTimer = setInterval(() => {
                const elapsed = Math.floor((Date.now() - recordingStartTime) / 1000);
                const minutes = Math.floor(elapsed / 60);
                const seconds = elapsed % 60;
                const timeStr = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
                status.innerHTML = `Recording... <span class="record-timer">${timeStr}</span> - Click to stop`;
            }, 1000);
        }

        function stopTimer() {
            if (recordingTimer) {
                clearInterval(recordingTimer);
                recordingTimer = null;
            }
        }

        function handleRecordedAudio(blob, type) {
            // Convert webm to wav for better compatibility
            convertToWav(blob).then(wavBlob => {
                const file = new File([wavBlob], `recorded_${type}_${Date.now()}.wav`, { type: 'audio/wav' });
                
                if (type === 'reference') {
                    referenceVoiceData = file;
                    showFileStatus('referenceStatus', `✅ Voice recorded successfully (${file.name})`, 'success');
                    
                    const url = URL.createObjectURL(file);
                    const player = document.getElementById('referencePlayer');
                    player.src = url;
                    player.style.display = 'block';
                    
                    document.getElementById('analyzeBtn').disabled = false;
                } else {
                    targetVoiceData = file;
                    showFileStatus('targetStatus', `✅ Voice recorded successfully (${file.name})`, 'success');
                    
                    const url = URL.createObjectURL(file);
                    const player = document.getElementById('targetPlayer');
                    player.src = url;
                    player.style.display = 'block';
                    
                    updateCloneButton();
                }
            });
        }

        async function convertToWav(webmBlob) {
            return new Promise((resolve) => {
                const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                const fileReader = new FileReader();
                
                fileReader.onload = async function(e) {
                    try {
                        const arrayBuffer = e.target.result;
                        const audioBuffer = await audioContext.decodeAudioData(arrayBuffer);
                        
                        // Create WAV file
                        const wavBuffer = audioBufferToWav(audioBuffer);
                        const wavBlob = new Blob([wavBuffer], { type: 'audio/wav' });
                        resolve(wavBlob);
                    } catch (error) {
                        console.warn('Could not convert to WAV, using original:', error);
                        resolve(webmBlob);
                    }
                };
                
                fileReader.readAsArrayBuffer(webmBlob);
            });
        }

        function audioBufferToWav(buffer) {
            const length = buffer.length;
            const numberOfChannels = buffer.numberOfChannels;
            const sampleRate = buffer.sampleRate;
            const arrayBuffer = new ArrayBuffer(44 + length * numberOfChannels * 2);
            const view = new DataView(arrayBuffer);
            
            // WAV header
            const writeString = (offset, string) => {
                for (let i = 0; i < string.length; i++) {
                    view.setUint8(offset + i, string.charCodeAt(i));
                }
            };
            
            writeString(0, 'RIFF');
            view.setUint32(4, 36 + length * numberOfChannels * 2, true);
            writeString(8, 'WAVE');
            writeString(12, 'fmt ');
            view.setUint32(16, 16, true);
            view.setUint16(20, 1, true);
            view.setUint16(22, numberOfChannels, true);
            view.setUint32(24, sampleRate, true);
            view.setUint32(28, sampleRate * numberOfChannels * 2, true);
            view.setUint16(32, numberOfChannels * 2, true);
            view.setUint16(34, 16, true);
            writeString(36, 'data');
            view.setUint32(40, length * numberOfChannels * 2, true);
            
            // Convert audio data
            let offset = 44;
            for (let i = 0; i < length; i++) {
                for (let channel = 0; channel < numberOfChannels; channel++) {
                    const sample = buffer.getChannelData(channel)[i];
                    const int16 = Math.max(-32768, Math.min(32767, sample * 32767));
                    view.setInt16(offset, int16, true);
                    offset += 2;
                }
            }
            
            return arrayBuffer;
        }

        function clearAll() {
            // Stop any ongoing recording
            if (mediaRecorder && mediaRecorder.state === 'recording') {
                stopRecording();
            }
            
            // Reset file inputs
            document.getElementById('referenceFile').value = '';
            document.getElementById('targetFile').value = '';
            document.getElementById('textInput').value = '';

            // Hide players
            document.getElementById('referencePlayer').style.display = 'none';
            document.getElementById('targetPlayer').style.display = 'none';

            // Hide status messages
            document.getElementById('referenceStatus').style.display = 'none';
            document.getElementById('targetStatus').style.display = 'none';
            
            // Reset recording UI
            updateRecordingUI('reference', false);
            updateRecordingUI('target', false);

            // Reset data
            referenceVoiceData = null;
            targetVoiceData = null;
            referenceVoiceId = null;
            currentRecordingType = null;

            // Disable buttons
            document.getElementById('analyzeBtn').disabled = true;
            document.getElementById('cloneBtn').disabled = true;

            // Hide output and errors
            hideOutput();
            hideError();
            hideLoading();
        }
    </script>
</body>
</html>