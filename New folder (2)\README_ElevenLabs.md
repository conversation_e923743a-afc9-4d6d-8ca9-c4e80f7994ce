# ElevenLabs Voice Cloning Web App

A professional web application for voice cloning using the ElevenLabs API with a Node.js backend to handle CORS restrictions.

## Features

- **Voice Cloning**: Upload multiple audio samples to create custom voice models
- **Voice Management**: View, select, and delete cloned voices
- **Text-to-Speech**: Generate speech using any available voice
- **Recording**: Record audio directly in the browser
- **Account Info**: View your ElevenLabs usage and limits
- **Professional UI**: Modern, responsive interface

## Setup Instructions

### Prerequisites

1. **Node.js**: Download and install from [nodejs.org](https://nodejs.org/)
2. **ElevenLabs Account**: Sign up at [elevenlabs.io](https://elevenlabs.io/)
3. **API Key**: Get your API key from [ElevenLabs Settings](https://elevenlabs.io/app/settings/api-keys)

### Installation

1. **Install dependencies:**
   ```bash
   npm install express cors multer form-data node-fetch nodemon
   ```
   
   Or run the setup script:
   ```bash
   setup_elevenlabs.bat
   ```

2. **Configure API Key:**
   - Open `elevenlabs_server.js`
   - Replace `'your_elevenlabs_api_key_here'` with your actual ElevenLabs API key
   - Save the file

3. **Start the server:**
   ```bash
   node elevenlabs_server.js
   ```

4. **Open your browser:**
   Navigate to `http://localhost:3001`

## How to Use

### 1. Clone a New Voice
1. Enter a name for your voice
2. Add a description (optional)
3. Upload audio files or record directly
4. Click "Clone Voice"
5. Wait for processing (can take 1-2 minutes)

### 2. Generate Speech
1. Select a voice from the "Available Voices" section
2. Enter the text you want to convert to speech
3. Click "Generate Speech"
4. Download the generated audio

### 3. Manage Voices
- View all available voices (built-in + cloned)
- Delete custom cloned voices (🗑️ button)
- Refresh the voices list

## API Endpoints

The server provides these endpoints:

- `GET /api/voices` - Get all available voices
- `POST /api/voices/clone` - Clone a new voice
- `POST /api/tts/:voice_id` - Generate speech
- `DELETE /api/voices/:voice_id` - Delete a cloned voice
- `GET /api/user` - Get account information

## Voice Cloning Tips

### Audio Quality Requirements
- **Duration**: 1-5 minutes of audio per sample
- **Quality**: Clear, high-quality recordings
- **Format**: WAV, MP3, or other common formats
- **Content**: Natural speech, avoid background noise
- **Multiple Samples**: Use 2-5 different audio samples for better results

### Best Practices
1. **Consistent Environment**: Record in a quiet space
2. **Natural Speech**: Use conversational tone
3. **Variety**: Include different emotions and speaking styles
4. **Length**: Each sample should be 30 seconds to 2 minutes
5. **Quality**: Use good microphone and avoid compression

## Troubleshooting

### API Key Issues
- Verify your API key is correct
- Check your ElevenLabs account is active
- Ensure you have sufficient credits

### Voice Cloning Fails
- Check audio file quality and format
- Ensure files are not too large (< 50MB each)
- Try with fewer or different audio samples

### Network Errors
- Verify server is running on port 3001
- Check firewall settings
- Ensure stable internet connection

## File Structure

```
├── elevenlabs_server.js           # Node.js backend server
├── elevenlabs_voice_cloning.html  # Web interface
├── elevenlabs_package.json        # Dependencies
├── setup_elevenlabs.bat          # Setup script
└── README_ElevenLabs.md          # This file
```

## Development

For development with auto-restart:
```bash
nodemon elevenlabs_server.js
```

## Production Deployment

1. Set API key as environment variable
2. Use HTTPS for secure microphone access
3. Configure proper CORS for your domain
4. Use process manager like PM2
5. Set up proper logging and monitoring

## ElevenLabs API Limits

- **Free Tier**: 10,000 characters/month
- **Starter**: 30,000 characters/month + voice cloning
- **Creator**: 100,000 characters/month + more features
- **Pro**: 500,000 characters/month + commercial license

Check your usage in the app's account information section.

## Support

- ElevenLabs Documentation: [docs.elevenlabs.io](https://docs.elevenlabs.io/)
- ElevenLabs Support: [help.elevenlabs.io](https://help.elevenlabs.io/)
- API Reference: [api.elevenlabs.io](https://api.elevenlabs.io/docs)
