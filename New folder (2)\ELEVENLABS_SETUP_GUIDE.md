# ElevenLabs Setup Guide

## Quick Start

### Step 1: Get Your ElevenLabs API Key

1. **Sign up/Login** to ElevenLabs:
   - Go to [elevenlabs.io](https://elevenlabs.io/)
   - Create an account or log in

2. **Get your API Key**:
   - Go to [Settings > API Keys](https://elevenlabs.io/app/settings/api-keys)
   - Copy your API key (starts with `sk-...`)

### Step 2: Configure the Server

1. **Open** `elevenlabs_server.js` in a text editor
2. **Find** this line (around line 10):
   ```javascript
   const ELEVENLABS_API_KEY = 'your_elevenlabs_api_key_here';
   ```
3. **Replace** `'your_elevenlabs_api_key_here'` with your actual API key:
   ```javascript
   const ELEVENLABS_API_KEY = 'sk-your-actual-api-key-here';
   ```
4. **Save** the file

### Step 3: Start the Server

```bash
node elevenlabs_server.js
```

### Step 4: Open the App

Open your browser and go to: **http://localhost:3001**

## Features Overview

### 🎤 Voice Cloning
- Upload multiple audio samples (WAV, MP3, etc.)
- Record directly in the browser
- Create custom voice models
- Professional quality results

### 👥 Voice Management
- View all available voices
- Select voices for speech generation
- Delete custom cloned voices
- Built-in ElevenLabs voices included

### 🔊 Text-to-Speech
- Generate speech with any voice
- Adjustable voice settings
- High-quality audio output
- Download generated audio

### 📊 Account Monitoring
- View character usage
- Check remaining credits
- Monitor voice clone limits

## Voice Cloning Best Practices

### Audio Requirements
- **Duration**: 1-5 minutes total audio
- **Quality**: Clear, noise-free recordings
- **Samples**: 2-5 different audio files work best
- **Content**: Natural conversational speech

### Recording Tips
1. Use a quiet environment
2. Speak naturally and clearly
3. Include different emotions/tones
4. Avoid background music or noise
5. Use a good quality microphone

## Troubleshooting

### Common Issues

**"API Error 401"**
- Check your API key is correct
- Ensure your ElevenLabs account is active

**"Insufficient credits"**
- Check your ElevenLabs subscription
- Upgrade your plan if needed

**"Voice cloning failed"**
- Ensure audio files are high quality
- Try with shorter audio samples
- Check file formats are supported

**"Cannot access microphone"**
- Allow microphone permissions in browser
- Use HTTPS in production

### Getting Help

- Check the console for detailed error messages
- Verify your internet connection
- Ensure the server is running on port 3001

## API Limits by Plan

| Plan | Characters/Month | Voice Cloning | Commercial Use |
|------|------------------|---------------|----------------|
| Free | 10,000 | ❌ | ❌ |
| Starter | 30,000 | ✅ | ❌ |
| Creator | 100,000 | ✅ | ✅ |
| Pro | 500,000 | ✅ | ✅ |

## Next Steps

1. **Test with built-in voices** first
2. **Clone your first voice** with good quality audio
3. **Experiment with different settings**
4. **Check your usage** in the account section

Enjoy creating amazing AI voices! 🎉
