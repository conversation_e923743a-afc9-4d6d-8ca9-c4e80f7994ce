const express = require('express');
const cors = require('cors');
const multer = require('multer');
const FormData = require('form-data');
const fetch = require('node-fetch');
const path = require('path');

const app = express();
const PORT = 3003;

// Resemble.ai API configuration
const RESEMBLE_API_KEY = 'yTmy7Y165IMOwLyFwV725Qtt'; // Replace with your actual API key
const RESEMBLE_BASE_URL = 'https://app.resemble.ai/api/v2';

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static('.'));

// Configure multer for file uploads
const upload = multer({ 
    storage: multer.memoryStorage(),
    limits: {
        fileSize: 100 * 1024 * 1024 // 100MB limit
    }
});

// Serve the HTML file
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'resemble_voice_cloning.html'));
});

// Default project UUID - we'll use a single project for simplicity
let DEFAULT_PROJECT_UUID = null;

// Initialize default project
async function initializeDefaultProject() {
    try {
        // First, try to get existing projects
        const response = await fetch(`${RESEMBLE_BASE_URL}/projects`, {
            method: 'GET',
            headers: {
                'Authorization': `Token token=${RESEMBLE_API_KEY}`,
                'Content-Type': 'application/json'
            }
        });

        if (response.ok) {
            const projects = await response.json();
            if (projects.items && projects.items.length > 0) {
                DEFAULT_PROJECT_UUID = projects.items[0].uuid;
                console.log(`Using existing project: ${DEFAULT_PROJECT_UUID}`);
                return;
            }
        }

        // Create default project if none exists
        const projectData = {
            name: 'Voice Cloning App',
            description: 'Default project for voice cloning',
            is_public: false,
            is_collaborative: false
        };

        const createResponse = await fetch(`${RESEMBLE_BASE_URL}/projects`, {
            method: 'POST',
            headers: {
                'Authorization': `Token token=${RESEMBLE_API_KEY}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(projectData)
        });

        if (createResponse.ok) {
            const result = await createResponse.json();
            DEFAULT_PROJECT_UUID = result.item.uuid;
            console.log(`Created default project: ${DEFAULT_PROJECT_UUID}`);
        } else {
            console.error('Failed to create default project');
        }

    } catch (error) {
        console.error('Error initializing default project:', error);
    }
}

// Get all voices (from default project)
app.get('/api/voices', async (req, res) => {
    try {
        if (!DEFAULT_PROJECT_UUID) {
            await initializeDefaultProject();
        }

        if (!DEFAULT_PROJECT_UUID) {
            return res.status(500).json({ error: 'Failed to initialize project' });
        }

        console.log('Fetching voices...');

        const response = await fetch(`${RESEMBLE_BASE_URL}/projects/${DEFAULT_PROJECT_UUID}/voices`, {
            method: 'GET',
            headers: {
                'Authorization': `Token token=${RESEMBLE_API_KEY}`,
                'Content-Type': 'application/json'
            }
        });

        if (response.ok) {
            const voices = await response.json();
            res.json(voices);
        } else {
            const errorText = await response.text();
            console.log('Error fetching voices:', errorText);
            res.status(response.status).json({
                error: 'Failed to fetch voices',
                details: errorText
            });
        }

    } catch (error) {
        console.error('Error fetching voices:', error);
        res.status(500).json({
            error: 'Internal server error',
            details: error.message
        });
    }
});

// Create voice (in default project)
app.post('/api/voices', upload.single('file'), async (req, res) => {
    try {
        if (!DEFAULT_PROJECT_UUID) {
            await initializeDefaultProject();
        }

        if (!DEFAULT_PROJECT_UUID) {
            return res.status(500).json({ error: 'Failed to initialize project' });
        }

        const { name } = req.body;

        console.log('Creating voice...');

        if (!req.file) {
            return res.status(400).json({ error: 'Audio file is required' });
        }

        if (!name) {
            return res.status(400).json({ error: 'Voice name is required' });
        }

        // Create FormData for Resemble API
        const formData = new FormData();
        formData.append('name', name);
        formData.append('file', req.file.buffer, {
            filename: req.file.originalname || 'audio.wav',
            contentType: req.file.mimetype || 'audio/wav'
        });

        const response = await fetch(`${RESEMBLE_BASE_URL}/projects/${DEFAULT_PROJECT_UUID}/voices`, {
            method: 'POST',
            headers: {
                'Authorization': `Token token=${RESEMBLE_API_KEY}`,
                ...formData.getHeaders()
            },
            body: formData
        });

        const responseText = await response.text();
        console.log(`Create Voice Response Status: ${response.status}`);
        console.log('Create Voice Response:', responseText);

        if (response.ok) {
            const result = JSON.parse(responseText);
            res.json(result);
        } else {
            let errorMessage = `API Error ${response.status}`;
            try {
                const errorJson = JSON.parse(responseText);
                errorMessage = errorJson.message || responseText;
            } catch {
                errorMessage = responseText;
            }

            res.status(response.status).json({
                error: errorMessage,
                details: responseText
            });
        }

    } catch (error) {
        console.error('Error creating voice:', error);
        res.status(500).json({
            error: 'Internal server error',
            details: error.message
        });
    }
});

// Generate speech (simplified)
app.post('/api/tts/:voice_uuid', async (req, res) => {
    try {
        if (!DEFAULT_PROJECT_UUID) {
            await initializeDefaultProject();
        }

        if (!DEFAULT_PROJECT_UUID) {
            return res.status(500).json({ error: 'Failed to initialize project' });
        }

        const { voice_uuid } = req.params;
        const { text, title } = req.body;

        console.log(`Creating clip with voice: ${voice_uuid}`);

        if (!text) {
            return res.status(400).json({ error: 'Text is required' });
        }

        const clipData = {
            voice_uuid: voice_uuid,
            body: text,
            title: title || `Speech_${Date.now()}`,
            sample_rate: 22050,
            output_format: 'mp3',
            precision: 'PCM_24',
            include_timestamps: false
        };

        const response = await fetch(`${RESEMBLE_BASE_URL}/projects/${DEFAULT_PROJECT_UUID}/clips`, {
            method: 'POST',
            headers: {
                'Authorization': `Token token=${RESEMBLE_API_KEY}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(clipData)
        });

        const responseText = await response.text();
        console.log(`Create Clip Response Status: ${response.status}`);

        if (response.ok) {
            const result = JSON.parse(responseText);

            // Poll for completion and return the audio URL directly
            const clipId = result.item.uuid;
            const audioUrl = await pollForClipCompletion(clipId);

            if (audioUrl) {
                res.json({ audio_url: audioUrl });
            } else {
                res.status(500).json({ error: 'Speech generation timed out' });
            }
        } else {
            let errorMessage = `API Error ${response.status}`;
            try {
                const errorJson = JSON.parse(responseText);
                errorMessage = errorJson.message || responseText;
            } catch {
                errorMessage = responseText;
            }

            res.status(response.status).json({
                error: errorMessage,
                details: responseText
            });
        }

    } catch (error) {
        console.error('Error creating clip:', error);
        res.status(500).json({
            error: 'Internal server error',
            details: error.message
        });
    }
});

// Helper function to poll for clip completion
async function pollForClipCompletion(clipId, maxAttempts = 30) {
    for (let attempt = 0; attempt < maxAttempts; attempt++) {
        try {
            const response = await fetch(`${RESEMBLE_BASE_URL}/projects/${DEFAULT_PROJECT_UUID}/clips/${clipId}`, {
                method: 'GET',
                headers: {
                    'Authorization': `Token token=${RESEMBLE_API_KEY}`,
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                const clip = await response.json();

                if (clip.item.status === 'completed' && clip.item.audio_src) {
                    return clip.item.audio_src;
                } else if (clip.item.status === 'failed') {
                    return null;
                }

                // Wait 2 seconds before next attempt
                await new Promise(resolve => setTimeout(resolve, 2000));
            } else {
                console.error('Error checking clip status:', response.status);
                return null;
            }
        } catch (error) {
            console.error('Error polling clip:', error);
            return null;
        }
    }

    return null; // Timed out
}

// Delete voice (simplified)
app.delete('/api/voices/:voice_uuid', async (req, res) => {
    try {
        if (!DEFAULT_PROJECT_UUID) {
            await initializeDefaultProject();
        }

        if (!DEFAULT_PROJECT_UUID) {
            return res.status(500).json({ error: 'Failed to initialize project' });
        }

        const { voice_uuid } = req.params;
        console.log(`Deleting voice: ${voice_uuid}`);

        const response = await fetch(`${RESEMBLE_BASE_URL}/projects/${DEFAULT_PROJECT_UUID}/voices/${voice_uuid}`, {
            method: 'DELETE',
            headers: {
                'Authorization': `Token token=${RESEMBLE_API_KEY}`
            }
        });

        if (response.ok) {
            res.json({ success: true, message: 'Voice deleted successfully' });
        } else {
            const errorText = await response.text();
            console.log('Delete Error:', errorText);
            res.status(response.status).json({
                error: 'Failed to delete voice',
                details: errorText
            });
        }

    } catch (error) {
        console.error('Error deleting voice:', error);
        res.status(500).json({
            error: 'Internal server error',
            details: error.message
        });
    }
});

// Health check endpoint
app.get('/api/health', (req, res) => {
    res.json({ 
        status: 'OK', 
        timestamp: new Date().toISOString(),
        service: 'Resemble.ai Voice Cloning'
    });
});

// Error handling middleware
app.use((error, req, res, next) => {
    console.error('Unhandled error:', error);
    res.status(500).json({ 
        error: 'Internal server error', 
        details: error.message 
    });
});

app.listen(PORT, async () => {
    console.log(`Resemble.ai server running on http://localhost:${PORT}`);
    console.log('Available endpoints:');
    console.log('  GET  /api/voices - Get all voices');
    console.log('  POST /api/voices - Create new voice');
    console.log('  POST /api/tts/:voice_id - Generate speech');
    console.log('  DELETE /api/voices/:voice_id - Delete voice');
    console.log('  GET  /api/health - Health check');
    console.log('');
    console.log('Initializing default project...');

    // Initialize default project on startup
    await initializeDefaultProject();

    if (DEFAULT_PROJECT_UUID) {
        console.log(`✅ Default project ready: ${DEFAULT_PROJECT_UUID}`);
    } else {
        console.log('❌ Failed to initialize default project. Check your API key.');
    }

    console.log('');
    console.log('Make sure your RESEMBLE_API_KEY is set correctly!');
});
