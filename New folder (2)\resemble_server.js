const express = require('express');
const cors = require('cors');
const multer = require('multer');
const FormData = require('form-data');
const fetch = require('node-fetch');
const path = require('path');

const app = express();
const PORT = 3002;

// Resemble.ai API configuration
const RESEMBLE_API_KEY = 'your_resemble_api_key_here'; // Replace with your actual API key
const RESEMBLE_BASE_URL = 'https://app.resemble.ai/api/v2';

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static('.'));

// Configure multer for file uploads
const upload = multer({ 
    storage: multer.memoryStorage(),
    limits: {
        fileSize: 100 * 1024 * 1024 // 100MB limit
    }
});

// Serve the HTML file
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'resemble_voice_cloning.html'));
});

// Get all projects (voice models)
app.get('/api/projects', async (req, res) => {
    try {
        console.log('Fetching projects...');
        
        const response = await fetch(`${RESEMBLE_BASE_URL}/projects`, {
            method: 'GET',
            headers: {
                'Authorization': `Token token=${RESEMBLE_API_KEY}`,
                'Content-Type': 'application/json'
            }
        });

        if (response.ok) {
            const projects = await response.json();
            res.json(projects);
        } else {
            const errorText = await response.text();
            console.log('Error fetching projects:', errorText);
            res.status(response.status).json({ 
                error: 'Failed to fetch projects',
                details: errorText 
            });
        }

    } catch (error) {
        console.error('Error fetching projects:', error);
        res.status(500).json({ 
            error: 'Internal server error', 
            details: error.message 
        });
    }
});

// Create new project (voice model)
app.post('/api/projects', async (req, res) => {
    try {
        console.log('Creating new project...');
        
        const { name, description } = req.body;
        
        if (!name) {
            return res.status(400).json({ error: 'Project name is required' });
        }

        const projectData = {
            name: name,
            description: description || 'Voice cloning project',
            is_public: false,
            is_collaborative: false
        };

        const response = await fetch(`${RESEMBLE_BASE_URL}/projects`, {
            method: 'POST',
            headers: {
                'Authorization': `Token token=${RESEMBLE_API_KEY}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(projectData)
        });

        const responseText = await response.text();
        console.log(`Create Project Response Status: ${response.status}`);
        console.log('Create Project Response:', responseText);

        if (response.ok) {
            const result = JSON.parse(responseText);
            res.json(result);
        } else {
            let errorMessage = `API Error ${response.status}`;
            try {
                const errorJson = JSON.parse(responseText);
                errorMessage = errorJson.message || responseText;
            } catch {
                errorMessage = responseText;
            }
            
            res.status(response.status).json({ 
                error: errorMessage,
                details: responseText 
            });
        }

    } catch (error) {
        console.error('Error creating project:', error);
        res.status(500).json({ 
            error: 'Internal server error', 
            details: error.message 
        });
    }
});

// Get voices for a project
app.get('/api/projects/:project_uuid/voices', async (req, res) => {
    try {
        const { project_uuid } = req.params;
        console.log(`Fetching voices for project: ${project_uuid}`);
        
        const response = await fetch(`${RESEMBLE_BASE_URL}/projects/${project_uuid}/voices`, {
            method: 'GET',
            headers: {
                'Authorization': `Token token=${RESEMBLE_API_KEY}`,
                'Content-Type': 'application/json'
            }
        });

        if (response.ok) {
            const voices = await response.json();
            res.json(voices);
        } else {
            const errorText = await response.text();
            console.log('Error fetching voices:', errorText);
            res.status(response.status).json({ 
                error: 'Failed to fetch voices',
                details: errorText 
            });
        }

    } catch (error) {
        console.error('Error fetching voices:', error);
        res.status(500).json({ 
            error: 'Internal server error', 
            details: error.message 
        });
    }
});

// Create voice in project
app.post('/api/projects/:project_uuid/voices', upload.single('file'), async (req, res) => {
    try {
        const { project_uuid } = req.params;
        const { name } = req.body;
        
        console.log(`Creating voice in project: ${project_uuid}`);
        
        if (!req.file) {
            return res.status(400).json({ error: 'Audio file is required' });
        }

        if (!name) {
            return res.status(400).json({ error: 'Voice name is required' });
        }

        // Create FormData for Resemble API
        const formData = new FormData();
        formData.append('name', name);
        formData.append('file', req.file.buffer, {
            filename: req.file.originalname || 'audio.wav',
            contentType: req.file.mimetype || 'audio/wav'
        });

        const response = await fetch(`${RESEMBLE_BASE_URL}/projects/${project_uuid}/voices`, {
            method: 'POST',
            headers: {
                'Authorization': `Token token=${RESEMBLE_API_KEY}`,
                ...formData.getHeaders()
            },
            body: formData
        });

        const responseText = await response.text();
        console.log(`Create Voice Response Status: ${response.status}`);
        console.log('Create Voice Response:', responseText);

        if (response.ok) {
            const result = JSON.parse(responseText);
            res.json(result);
        } else {
            let errorMessage = `API Error ${response.status}`;
            try {
                const errorJson = JSON.parse(responseText);
                errorMessage = errorJson.message || responseText;
            } catch {
                errorMessage = responseText;
            }
            
            res.status(response.status).json({ 
                error: errorMessage,
                details: responseText 
            });
        }

    } catch (error) {
        console.error('Error creating voice:', error);
        res.status(500).json({ 
            error: 'Internal server error', 
            details: error.message 
        });
    }
});

// Generate speech (clips)
app.post('/api/projects/:project_uuid/clips', async (req, res) => {
    try {
        const { project_uuid } = req.params;
        const { voice_uuid, text, title } = req.body;
        
        console.log(`Generating speech for project: ${project_uuid}, voice: ${voice_uuid}`);
        
        if (!voice_uuid || !text) {
            return res.status(400).json({ error: 'Voice UUID and text are required' });
        }

        const clipData = {
            voice_uuid: voice_uuid,
            body: text,
            title: title || `Speech_${Date.now()}`,
            sample_rate: 22050,
            output_format: 'mp3',
            precision: 'PCM_24',
            include_timestamps: false
        };

        const response = await fetch(`${RESEMBLE_BASE_URL}/projects/${project_uuid}/clips`, {
            method: 'POST',
            headers: {
                'Authorization': `Token token=${RESEMBLE_API_KEY}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(clipData)
        });

        const responseText = await response.text();
        console.log(`Generate Speech Response Status: ${response.status}`);

        if (response.ok) {
            const result = JSON.parse(responseText);
            res.json(result);
        } else {
            let errorMessage = `Speech Generation Error ${response.status}`;
            try {
                const errorJson = JSON.parse(responseText);
                errorMessage = errorJson.message || responseText;
            } catch {
                errorMessage = responseText;
            }
            
            res.status(response.status).json({ 
                error: errorMessage,
                details: responseText 
            });
        }

    } catch (error) {
        console.error('Error generating speech:', error);
        res.status(500).json({ 
            error: 'Internal server error', 
            details: error.message 
        });
    }
});

// Get clip status and download URL
app.get('/api/projects/:project_uuid/clips/:clip_uuid', async (req, res) => {
    try {
        const { project_uuid, clip_uuid } = req.params;
        console.log(`Getting clip status: ${clip_uuid}`);

        const response = await fetch(`${RESEMBLE_BASE_URL}/projects/${project_uuid}/clips/${clip_uuid}`, {
            method: 'GET',
            headers: {
                'Authorization': `Token token=${RESEMBLE_API_KEY}`,
                'Content-Type': 'application/json'
            }
        });

        if (response.ok) {
            const clip = await response.json();
            res.json(clip);
        } else {
            const errorText = await response.text();
            console.log('Error fetching clip:', errorText);
            res.status(response.status).json({ 
                error: 'Failed to fetch clip',
                details: errorText 
            });
        }

    } catch (error) {
        console.error('Error fetching clip:', error);
        res.status(500).json({ 
            error: 'Internal server error', 
            details: error.message 
        });
    }
});

// Delete voice
app.delete('/api/projects/:project_uuid/voices/:voice_uuid', async (req, res) => {
    try {
        const { project_uuid, voice_uuid } = req.params;
        console.log(`Deleting voice: ${voice_uuid} from project: ${project_uuid}`);

        const response = await fetch(`${RESEMBLE_BASE_URL}/projects/${project_uuid}/voices/${voice_uuid}`, {
            method: 'DELETE',
            headers: {
                'Authorization': `Token token=${RESEMBLE_API_KEY}`
            }
        });

        if (response.ok) {
            res.json({ success: true, message: 'Voice deleted successfully' });
        } else {
            const errorText = await response.text();
            console.log('Delete Error:', errorText);
            res.status(response.status).json({ 
                error: 'Failed to delete voice',
                details: errorText 
            });
        }

    } catch (error) {
        console.error('Error deleting voice:', error);
        res.status(500).json({ 
            error: 'Internal server error', 
            details: error.message 
        });
    }
});

// Delete project
app.delete('/api/projects/:project_uuid', async (req, res) => {
    try {
        const { project_uuid } = req.params;
        console.log(`Deleting project: ${project_uuid}`);

        const response = await fetch(`${RESEMBLE_BASE_URL}/projects/${project_uuid}`, {
            method: 'DELETE',
            headers: {
                'Authorization': `Token token=${RESEMBLE_API_KEY}`
            }
        });

        if (response.ok) {
            res.json({ success: true, message: 'Project deleted successfully' });
        } else {
            const errorText = await response.text();
            console.log('Delete Project Error:', errorText);
            res.status(response.status).json({ 
                error: 'Failed to delete project',
                details: errorText 
            });
        }

    } catch (error) {
        console.error('Error deleting project:', error);
        res.status(500).json({ 
            error: 'Internal server error', 
            details: error.message 
        });
    }
});

// Health check endpoint
app.get('/api/health', (req, res) => {
    res.json({ 
        status: 'OK', 
        timestamp: new Date().toISOString(),
        service: 'Resemble.ai Voice Cloning'
    });
});

// Error handling middleware
app.use((error, req, res, next) => {
    console.error('Unhandled error:', error);
    res.status(500).json({ 
        error: 'Internal server error', 
        details: error.message 
    });
});

app.listen(PORT, () => {
    console.log(`Resemble.ai Server running at http://localhost:${PORT}`);
    console.log('Resemble.ai Voice Cloning Server Started');
    console.log(`Open your browser and navigate to http://localhost:${PORT}`);
    console.log('');
    console.log('⚠️  IMPORTANT: Update your Resemble.ai API key in resemble_server.js');
    console.log('   Get your API key from: https://app.resemble.ai/account');
});
