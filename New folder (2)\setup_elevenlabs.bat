@echo off
echo ElevenLabs Voice Cloning Setup
echo ===============================

echo.
echo Installing Node.js dependencies for ElevenLabs...
npm install --package-lock-only
npm install express cors multer form-data node-fetch nodemon

echo.
echo Setup complete!
echo.
echo IMPORTANT: Before starting the server, you need to:
echo 1. Get your ElevenLabs API key from: https://elevenlabs.io/app/settings/api-keys
echo 2. Edit elevenlabs_server.js and replace 'your_elevenlabs_api_key_here' with your actual API key
echo.
echo To start the ElevenLabs server, run:
echo   node elevenlabs_server.js
echo.
echo Then open your browser to: http://localhost:3001
echo.
pause
