const express = require('express');
const cors = require('cors');
const multer = require('multer');
const FormData = require('form-data');
const fetch = require('node-fetch');
const path = require('path');

const app = express();
const PORT = 3001;

// ElevenLabs API configuration
const ELEVENLABS_API_KEY = '***************************************************'; // Replace with your actual API key
const ELEVENLABS_BASE_URL = 'https://api.elevenlabs.io/v1';

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static('.'));

// Configure multer for file uploads
const upload = multer({ 
    storage: multer.memoryStorage(),
    limits: {
        fileSize: 50 * 1024 * 1024 // 50MB limit
    }
});

// Serve the HTML file
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'elevenlabs_voice_cloning.html'));
});

// Get available voices
app.get('/api/voices', async (req, res) => {
    try {
        console.log('Fetching available voices...');
        
        const response = await fetch(`${ELEVENLABS_BASE_URL}/voices`, {
            method: 'GET',
            headers: {
                'xi-api-key': ELEVENLABS_API_KEY
            }
        });

        if (response.ok) {
            const voices = await response.json();
            res.json(voices);
        } else {
            const errorText = await response.text();
            console.log('Error fetching voices:', errorText);
            res.status(response.status).json({ 
                error: 'Failed to fetch voices',
                details: errorText 
            });
        }

    } catch (error) {
        console.error('Error fetching voices:', error);
        res.status(500).json({ 
            error: 'Internal server error', 
            details: error.message 
        });
    }
});

// Clone voice endpoint
app.post('/api/voices/clone', upload.array('files'), async (req, res) => {
    try {
        console.log('Cloning voice...');
        
        if (!req.files || req.files.length === 0) {
            return res.status(400).json({ error: 'No audio files provided' });
        }

        const { name, description } = req.body;
        
        if (!name) {
            return res.status(400).json({ error: 'Voice name is required' });
        }

        // Create FormData for ElevenLabs API
        const formData = new FormData();
        formData.append('name', name);
        formData.append('description', description || 'Cloned voice');

        // Add audio files
        req.files.forEach((file, index) => {
            formData.append('files', file.buffer, {
                filename: file.originalname || `audio_${index}.wav`,
                contentType: file.mimetype || 'audio/wav'
            });
        });

        const response = await fetch(`${ELEVENLABS_BASE_URL}/voices/add`, {
            method: 'POST',
            headers: {
                'xi-api-key': ELEVENLABS_API_KEY,
                ...formData.getHeaders()
            },
            body: formData
        });

        const responseText = await response.text();
        console.log(`Voice Clone Response Status: ${response.status}`);
        console.log('Voice Clone Response:', responseText);

        if (response.ok) {
            const result = JSON.parse(responseText);
            res.json(result);
        } else {
            let errorMessage = `API Error ${response.status}`;
            try {
                const errorJson = JSON.parse(responseText);
                errorMessage = errorJson.detail?.message || errorJson.message || responseText;
            } catch {
                errorMessage = responseText;
            }
            
            res.status(response.status).json({ 
                error: errorMessage,
                details: responseText 
            });
        }

    } catch (error) {
        console.error('Error cloning voice:', error);
        res.status(500).json({ 
            error: 'Internal server error', 
            details: error.message 
        });
    }
});

// Text-to-speech endpoint
app.post('/api/tts/:voice_id', async (req, res) => {
    try {
        const { voice_id } = req.params;
        const { text, voice_settings } = req.body;
        
        console.log(`Generating speech for voice: ${voice_id}`);
        
        if (!text) {
            return res.status(400).json({ error: 'Text is required' });
        }

        const ttsPayload = {
            text: text,
            model_id: "eleven_multilingual_v2",
            voice_settings: voice_settings || {
                stability: 0.5,
                similarity_boost: 0.8,
                style: 0.0,
                use_speaker_boost: true
            }
        };

        const response = await fetch(`${ELEVENLABS_BASE_URL}/text-to-speech/${voice_id}`, {
            method: 'POST',
            headers: {
                'xi-api-key': ELEVENLABS_API_KEY,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(ttsPayload)
        });

        console.log(`TTS Response Status: ${response.status}`);

        if (response.ok) {
            const audioBuffer = await response.buffer();
            res.set({
                'Content-Type': 'audio/mpeg',
                'Content-Length': audioBuffer.length
            });
            res.send(audioBuffer);
        } else {
            const errorText = await response.text();
            console.log('TTS Error:', errorText);
            
            let errorMessage = `TTS Error ${response.status}`;
            try {
                const errorJson = JSON.parse(errorText);
                errorMessage = errorJson.detail?.message || errorJson.message || errorText;
            } catch {
                errorMessage = errorText;
            }
            
            res.status(response.status).json({ 
                error: errorMessage,
                details: errorText 
            });
        }

    } catch (error) {
        console.error('Error generating speech:', error);
        res.status(500).json({ 
            error: 'Internal server error', 
            details: error.message 
        });
    }
});

// Delete voice endpoint
app.delete('/api/voices/:voice_id', async (req, res) => {
    try {
        const { voice_id } = req.params;
        console.log(`Deleting voice: ${voice_id}`);

        const response = await fetch(`${ELEVENLABS_BASE_URL}/voices/${voice_id}`, {
            method: 'DELETE',
            headers: {
                'xi-api-key': ELEVENLABS_API_KEY
            }
        });

        if (response.ok) {
            res.json({ success: true, message: 'Voice deleted successfully' });
        } else {
            const errorText = await response.text();
            console.log('Delete Error:', errorText);
            res.status(response.status).json({ 
                error: 'Failed to delete voice',
                details: errorText 
            });
        }

    } catch (error) {
        console.error('Error deleting voice:', error);
        res.status(500).json({ 
            error: 'Internal server error', 
            details: error.message 
        });
    }
});

// Get user info endpoint
app.get('/api/user', async (req, res) => {
    try {
        const response = await fetch(`${ELEVENLABS_BASE_URL}/user`, {
            method: 'GET',
            headers: {
                'xi-api-key': ELEVENLABS_API_KEY
            }
        });

        if (response.ok) {
            const user = await response.json();
            res.json(user);
        } else {
            const errorText = await response.text();
            res.status(response.status).json({ 
                error: 'Failed to fetch user info',
                details: errorText 
            });
        }

    } catch (error) {
        console.error('Error fetching user info:', error);
        res.status(500).json({ 
            error: 'Internal server error', 
            details: error.message 
        });
    }
});

// Health check endpoint
app.get('/api/health', (req, res) => {
    res.json({ 
        status: 'OK', 
        timestamp: new Date().toISOString(),
        service: 'ElevenLabs Voice Cloning'
    });
});

// Error handling middleware
app.use((error, req, res, next) => {
    console.error('Unhandled error:', error);
    res.status(500).json({ 
        error: 'Internal server error', 
        details: error.message 
    });
});

app.listen(PORT, () => {
    console.log(`ElevenLabs Server running at http://localhost:${PORT}`);
    console.log('ElevenLabs Voice Cloning Server Started');
    console.log(`Open your browser and navigate to http://localhost:${PORT}`);
    console.log('');
    console.log('⚠️  IMPORTANT: Update your ElevenLabs API key in elevenlabs_server.js');
    console.log('   Get your API key from: https://elevenlabs.io/app/settings/api-keys');
});
