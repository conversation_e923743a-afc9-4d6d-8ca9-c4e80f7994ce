<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Resemble.ai Voice Cloning App</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
        }

        .header h1 {
            color: #2d3748;
            font-size: 2.5rem;
            margin-bottom: 10px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .header p {
            color: #718096;
            font-size: 1.1rem;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }

        .section {
            background: #f7fafc;
            border-radius: 15px;
            padding: 25px;
            border: 2px solid #e2e8f0;
            transition: all 0.3s ease;
        }

        .section:hover {
            border-color: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.1);
        }

        .section h2 {
            color: #2d3748;
            margin-bottom: 20px;
            font-size: 1.5rem;
            display: flex;
            align-items: center;
        }

        .section h2::before {
            margin-right: 10px;
            font-size: 1.2rem;
        }

        .project-section h2::before { content: '📁'; }
        .voice-section h2::before { content: '🎤'; }
        .tts-section h2::before { content: '🔊'; }

        .input-options {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .file-input-wrapper {
            flex: 1;
            min-width: 200px;
        }

        .record-wrapper {
            flex: 1;
            min-width: 200px;
        }

        .btn-record {
            width: 100%;
            padding: 15px 20px;
            border: 2px solid #667eea;
            background: transparent;
            color: #667eea;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
            font-size: 1rem;
        }

        .btn-record:hover {
            background: #667eea;
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }

        .btn-record.recording {
            background: #e53e3e;
            color: white;
            border-color: #e53e3e;
            animation: pulse 1.5s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .file-input {
            display: none;
        }

        .file-input-label {
            display: block;
            padding: 15px 20px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-radius: 10px;
            cursor: pointer;
            text-align: center;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .file-input-label:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }

        .file-status {
            margin-top: 10px;
            padding: 10px;
            border-radius: 8px;
            font-size: 0.9rem;
        }

        .file-status.success {
            background: #c6f6d5;
            color: #2f855a;
            border: 1px solid #9ae6b4;
        }

        .file-status.error {
            background: #fed7d7;
            color: #c53030;
            border: 1px solid #feb2b2;
        }

        .audio-player {
            width: 100%;
            margin: 15px 0;
            border-radius: 8px;
        }

        .controls {
            display: flex;
            gap: 15px;
            margin-top: 20px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
            font-size: 1rem;
            flex: 1;
            min-width: 120px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .btn-secondary {
            background: #e2e8f0;
            color: #4a5568;
        }

        .btn-danger {
            background: #e53e3e;
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .text-input {
            width: 100%;
            padding: 15px;
            border: 2px solid #e2e8f0;
            border-radius: 10px;
            font-size: 1rem;
            margin-bottom: 20px;
            transition: border-color 0.3s ease;
        }

        .text-input:focus {
            outline: none;
            border-color: #667eea;
        }

        .list-container {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            margin-bottom: 15px;
        }

        .list-item {
            padding: 15px;
            border-bottom: 1px solid #e2e8f0;
            cursor: pointer;
            transition: background-color 0.3s ease;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .list-item:hover {
            background-color: #f1f5f9;
        }

        .list-item.selected {
            background-color: #e6f3ff;
            border-left: 4px solid #667eea;
        }

        .item-info {
            flex: 1;
        }

        .item-name {
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 5px;
        }

        .item-description {
            font-size: 0.9rem;
            color: #718096;
        }

        .item-actions {
            display: flex;
            gap: 10px;
        }

        .btn-small {
            padding: 6px 12px;
            font-size: 0.8rem;
            min-width: auto;
        }

        .output-section {
            grid-column: 1 / -1;
            background: #f7fafc;
            border-radius: 15px;
            padding: 25px;
            border: 2px solid #e2e8f0;
        }

        .output-section h2::before {
            content: '🎵';
            margin-right: 10px;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }

        .loading.show {
            display: block;
        }

        .spinner {
            border: 4px solid #e2e8f0;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .error-message {
            background: #fed7d7;
            color: #c53030;
            padding: 15px;
            border-radius: 8px;
            margin-top: 15px;
            border: 1px solid #feb2b2;
        }

        .info-box {
            background: #e6fffa;
            border: 1px solid #81e6d9;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .info-box h3 {
            color: #234e52;
            margin-bottom: 10px;
        }

        .workflow-steps {
            background: #f0f8ff;
            border: 1px solid #bee3f8;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .workflow-steps h3 {
            color: #2b6cb0;
            margin-bottom: 15px;
        }

        .step {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            padding: 10px;
            background: white;
            border-radius: 6px;
        }

        .step-number {
            background: #667eea;
            color: white;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.8rem;
            font-weight: bold;
            margin-right: 10px;
        }

        @media (max-width: 1024px) {
            .main-content {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .controls {
                flex-direction: column;
            }
            
            .input-options {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Resemble.ai Voice Cloning</h1>
            <p>Professional voice cloning with real-time synthesis</p>
        </div>

        <!-- Workflow Guide -->
        <div class="workflow-steps">
            <h3>How to Use Resemble.ai Voice Cloning</h3>
            <div class="step">
                <div class="step-number">1</div>
                <div>Create a new project or select an existing one</div>
            </div>
            <div class="step">
                <div class="step-number">2</div>
                <div>Upload audio samples to create a voice model</div>
            </div>
            <div class="step">
                <div class="step-number">3</div>
                <div>Select a voice and generate speech with your text</div>
            </div>
        </div>

        <div class="main-content">
            <!-- Project Management Section -->
            <div class="section project-section">
                <h2>Projects</h2>
                
                <input type="text" id="projectName" class="text-input" placeholder="Enter project name..." maxlength="50">
                
                <textarea id="projectDescription" class="text-input" placeholder="Project description (optional)..." rows="3"></textarea>
                
                <div class="controls" style="margin-top: 0; margin-bottom: 15px;">
                    <button id="createProjectBtn" class="btn btn-primary" disabled>
                        ➕ Create Project
                    </button>
                    <button id="refreshProjectsBtn" class="btn btn-secondary">
                        🔄 Refresh
                    </button>
                </div>
                
                <div id="projectsList" class="list-container">
                    <div style="padding: 20px; text-align: center; color: #718096;">
                        Loading projects...
                    </div>
                </div>
                
                <div id="selectedProjectInfo" style="margin-top: 15px; padding: 10px; background: #e6f3ff; border-radius: 8px; display: none;">
                    <strong>Selected Project:</strong> <span id="selectedProjectName">None</span>
                </div>
            </div>

            <!-- Voice Creation Section -->
            <div class="section voice-section">
                <h2>Voice Models</h2>
                
                <input type="text" id="voiceName" class="text-input" placeholder="Enter voice name..." maxlength="50">
                
                <div class="input-options">
                    <div class="file-input-wrapper">
                        <input type="file" id="voiceFile" class="file-input" accept="audio/*">
                        <label for="voiceFile" class="file-input-label">
                            📁 Upload Audio
                        </label>
                    </div>
                    <div class="record-wrapper">
                        <button id="recordBtn" class="btn-record">
                            🎙️ Record Voice
                        </button>
                    </div>
                </div>
                
                <div id="voiceStatus" class="file-status" style="display: none;"></div>
                <div id="recordedAudio"></div>
                
                <div class="controls">
                    <button id="createVoiceBtn" class="btn btn-primary" disabled>
                        🎤 Create Voice
                    </button>
                    <button id="refreshVoicesBtn" class="btn btn-secondary">
                        🔄 Refresh Voices
                    </button>
                </div>
                
                <div id="voicesList" class="list-container">
                    <div style="padding: 20px; text-align: center; color: #718096;">
                        Select a project first
                    </div>
                </div>
            </div>

            <!-- Text-to-Speech Section -->
            <div class="section tts-section">
                <h2>Generate Speech</h2>
                
                <textarea 
                    id="ttsText" 
                    class="text-input" 
                    placeholder="Enter the text you want to convert to speech..."
                    rows="6"
                ></textarea>
                
                <input type="text" id="clipTitle" class="text-input" placeholder="Audio clip title (optional)..." maxlength="100">
                
                <div class="controls">
                    <button id="generateSpeechBtn" class="btn btn-primary" disabled>
                        🎵 Generate Speech
                    </button>
                </div>
                
                <div id="selectedVoiceInfo" style="margin-top: 15px; padding: 10px; background: #e6f3ff; border-radius: 8px; display: none;">
                    <strong>Selected Voice:</strong> <span id="selectedVoiceName">None</span>
                </div>
            </div>
        </div>

        <!-- Output Section -->
        <div class="output-section">
            <h2>Generated Audio</h2>
            <div id="loading" class="loading">
                <div class="spinner"></div>
                <p>Processing... This may take a few moments.</p>
            </div>
            <div id="outputContainer" style="display: none;">
                <audio id="outputPlayer" class="audio-player" controls></audio>
                <div class="controls">
                    <button id="downloadBtn" class="btn btn-primary">
                        ⬇️ Download Audio
                    </button>
                </div>
            </div>
            <div id="errorContainer" style="display: none;">
                <div class="error-message" id="errorMessage"></div>
            </div>
        </div>
    </div>

    <script>
        // Server configuration
        const SERVER_BASE_URL = 'http://localhost:3002';
        
        // Global variables
        let selectedProjectId = null;
        let selectedVoiceId = null;
        let recordedAudioFile = null;
        let mediaRecorder = null;
        let recordingTimer = null;
        let recordingStartTime = null;

        // Initialize the application
        document.addEventListener('DOMContentLoaded', function() {
            loadProjects();
            setupEventListeners();
        });

        function setupEventListeners() {
            // File inputs
            document.getElementById('voiceFile').addEventListener('change', handleVoiceFile);

            // Recording
            document.getElementById('recordBtn').addEventListener('click', toggleRecording);

            // Buttons
            document.getElementById('createProjectBtn').addEventListener('click', createProject);
            document.getElementById('refreshProjectsBtn').addEventListener('click', loadProjects);
            document.getElementById('createVoiceBtn').addEventListener('click', createVoice);
            document.getElementById('refreshVoicesBtn').addEventListener('click', loadVoices);
            document.getElementById('generateSpeechBtn').addEventListener('click', generateSpeech);
            document.getElementById('downloadBtn').addEventListener('click', downloadAudio);

            // Text inputs
            document.getElementById('projectName').addEventListener('input', updateCreateProjectButton);
            document.getElementById('voiceName').addEventListener('input', updateCreateVoiceButton);
            document.getElementById('ttsText').addEventListener('input', updateGenerateButton);
        }

        // Load projects
        async function loadProjects() {
            try {
                showProjectsLoading();
                const response = await fetch(`${SERVER_BASE_URL}/api/projects`);

                if (response.ok) {
                    const data = await response.json();
                    displayProjects(data.items || []);
                } else {
                    const error = await response.json();
                    showProjectsError(error.error || 'Failed to load projects');
                }
            } catch (error) {
                console.error('Error loading projects:', error);
                showProjectsError('Network error loading projects');
            }
        }

        function showProjectsLoading() {
            document.getElementById('projectsList').innerHTML = `
                <div style="padding: 20px; text-align: center; color: #718096;">
                    <div class="spinner" style="width: 20px; height: 20px; margin: 0 auto 10px;"></div>
                    Loading projects...
                </div>
            `;
        }

        function showProjectsError(message) {
            document.getElementById('projectsList').innerHTML = `
                <div style="padding: 20px; text-align: center; color: #c53030;">
                    ❌ ${message}
                </div>
            `;
        }

        function displayProjects(projects) {
            const projectsList = document.getElementById('projectsList');

            if (projects.length === 0) {
                projectsList.innerHTML = `
                    <div style="padding: 20px; text-align: center; color: #718096;">
                        No projects found. Create your first project!
                    </div>
                `;
                return;
            }

            projectsList.innerHTML = projects.map(project => `
                <div class="list-item" data-project-id="${project.uuid}" onclick="selectProject('${project.uuid}', '${project.name}')">
                    <div class="item-info">
                        <div class="item-name">${project.name}</div>
                        <div class="item-description">${project.description || 'No description'}</div>
                    </div>
                    <div class="item-actions">
                        <button class="btn btn-danger btn-small" onclick="event.stopPropagation(); deleteProject('${project.uuid}', '${project.name}')">
                            🗑️
                        </button>
                    </div>
                </div>
            `).join('');
        }

        function selectProject(projectId, projectName) {
            // Remove previous selection
            document.querySelectorAll('#projectsList .list-item').forEach(item => {
                item.classList.remove('selected');
            });

            // Add selection to clicked item
            document.querySelector(`[data-project-id="${projectId}"]`).classList.add('selected');

            selectedProjectId = projectId;
            document.getElementById('selectedProjectName').textContent = projectName;
            document.getElementById('selectedProjectInfo').style.display = 'block';

            // Load voices for this project
            loadVoices();
            updateCreateVoiceButton();
        }

        // Create new project
        async function createProject() {
            const projectName = document.getElementById('projectName').value.trim();
            const projectDescription = document.getElementById('projectDescription').value.trim();

            if (!projectName) {
                showError('Please enter a project name.');
                return;
            }

            try {
                showLoading();
                hideOutput();
                hideError();

                const response = await fetch(`${SERVER_BASE_URL}/api/projects`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        name: projectName,
                        description: projectDescription
                    })
                });

                if (response.ok) {
                    const result = await response.json();
                    hideLoading();

                    // Clear form and refresh projects
                    document.getElementById('projectName').value = '';
                    document.getElementById('projectDescription').value = '';
                    updateCreateProjectButton();

                    loadProjects();

                    // Auto-select the new project
                    setTimeout(() => {
                        selectProject(result.item.uuid, projectName);
                    }, 1000);

                } else {
                    const error = await response.json();
                    throw new Error(error.error || 'Failed to create project');
                }

            } catch (error) {
                console.error('Error creating project:', error);
                hideLoading();
                showError(`Project creation failed: ${error.message}`);
            }
        }

        // Delete project
        async function deleteProject(projectId, projectName) {
            if (!confirm(`Are you sure you want to delete the project "${projectName}"? This will also delete all voices in this project. This action cannot be undone.`)) {
                return;
            }

            try {
                const response = await fetch(`${SERVER_BASE_URL}/api/projects/${projectId}`, {
                    method: 'DELETE'
                });

                if (response.ok) {
                    loadProjects(); // Refresh the list

                    // Clear selection if deleted project was selected
                    if (selectedProjectId === projectId) {
                        selectedProjectId = null;
                        selectedVoiceId = null;
                        document.getElementById('selectedProjectInfo').style.display = 'none';
                        document.getElementById('selectedVoiceInfo').style.display = 'none';
                        document.getElementById('voicesList').innerHTML = `
                            <div style="padding: 20px; text-align: center; color: #718096;">
                                Select a project first
                            </div>
                        `;
                        updateCreateVoiceButton();
                        updateGenerateButton();
                    }

                } else {
                    const error = await response.json();
                    showError(`Failed to delete project: ${error.error}`);
                }

            } catch (error) {
                console.error('Error deleting project:', error);
                showError(`Error deleting project: ${error.message}`);
            }
        }

        // Load voices for selected project
        async function loadVoices() {
            if (!selectedProjectId) {
                document.getElementById('voicesList').innerHTML = `
                    <div style="padding: 20px; text-align: center; color: #718096;">
                        Select a project first
                    </div>
                `;
                return;
            }

            try {
                showVoicesLoading();
                const response = await fetch(`${SERVER_BASE_URL}/api/projects/${selectedProjectId}/voices`);

                if (response.ok) {
                    const data = await response.json();
                    displayVoices(data.items || []);
                } else {
                    const error = await response.json();
                    showVoicesError(error.error || 'Failed to load voices');
                }
            } catch (error) {
                console.error('Error loading voices:', error);
                showVoicesError('Network error loading voices');
            }
        }

        function showVoicesLoading() {
            document.getElementById('voicesList').innerHTML = `
                <div style="padding: 20px; text-align: center; color: #718096;">
                    <div class="spinner" style="width: 20px; height: 20px; margin: 0 auto 10px;"></div>
                    Loading voices...
                </div>
            `;
        }

        function showVoicesError(message) {
            document.getElementById('voicesList').innerHTML = `
                <div style="padding: 20px; text-align: center; color: #c53030;">
                    ❌ ${message}
                </div>
            `;
        }

        function displayVoices(voices) {
            const voicesList = document.getElementById('voicesList');

            if (voices.length === 0) {
                voicesList.innerHTML = `
                    <div style="padding: 20px; text-align: center; color: #718096;">
                        No voices in this project. Create your first voice!
                    </div>
                `;
                return;
            }

            voicesList.innerHTML = voices.map(voice => `
                <div class="list-item" data-voice-id="${voice.uuid}" onclick="selectVoice('${voice.uuid}', '${voice.name}')">
                    <div class="item-info">
                        <div class="item-name">${voice.name}</div>
                        <div class="item-description">Status: ${voice.status || 'Unknown'}</div>
                    </div>
                    <div class="item-actions">
                        <button class="btn btn-danger btn-small" onclick="event.stopPropagation(); deleteVoice('${voice.uuid}', '${voice.name}')">
                            🗑️
                        </button>
                    </div>
                </div>
            `).join('');
        }

        function selectVoice(voiceId, voiceName) {
            // Remove previous selection
            document.querySelectorAll('#voicesList .list-item').forEach(item => {
                item.classList.remove('selected');
            });

            // Add selection to clicked item
            document.querySelector(`[data-voice-id="${voiceId}"]`).classList.add('selected');

            selectedVoiceId = voiceId;
            document.getElementById('selectedVoiceName').textContent = voiceName;
            document.getElementById('selectedVoiceInfo').style.display = 'block';

            updateGenerateButton();
        }

        // Handle file upload for voice creation
        function handleVoiceFile(event) {
            const file = event.target.files[0];
            if (!file) return;

            recordedAudioFile = file;
            showFileStatus('voiceStatus', `✅ File selected: ${file.name}`, 'success');
            displayRecordedAudio(file);
            updateCreateVoiceButton();
        }

        function displayRecordedAudio(file) {
            const container = document.getElementById('recordedAudio');
            const audioUrl = URL.createObjectURL(file);

            container.innerHTML = `
                <div style="margin: 10px 0; padding: 10px; background: #f1f5f9; border-radius: 8px;">
                    <div style="margin-bottom: 10px;">
                        <strong>${file.name}</strong>
                        <span style="font-size: 0.9rem; color: #718096; margin-left: 10px;">
                            ${(file.size / 1024 / 1024).toFixed(2)} MB
                        </span>
                    </div>
                    <audio controls style="width: 100%;">
                        <source src="${audioUrl}" type="${file.type}">
                        Your browser does not support the audio element.
                    </audio>
                </div>
            `;
        }

        // Recording functionality
        async function toggleRecording() {
            if (mediaRecorder && mediaRecorder.state === 'recording') {
                stopRecording();
            } else {
                startRecording();
            }
        }

        async function startRecording() {
            try {
                const stream = await navigator.mediaDevices.getUserMedia({
                    audio: {
                        echoCancellation: true,
                        noiseSuppression: true,
                        sampleRate: 44100
                    }
                });

                mediaRecorder = new MediaRecorder(stream, {
                    mimeType: 'audio/webm;codecs=opus'
                });

                let recordedChunks = [];
                recordingStartTime = Date.now();

                mediaRecorder.ondataavailable = (event) => {
                    if (event.data.size > 0) {
                        recordedChunks.push(event.data);
                    }
                };

                mediaRecorder.onstop = () => {
                    const blob = new Blob(recordedChunks, { type: 'audio/webm' });
                    const file = new File([blob], `recorded_${Date.now()}.webm`, { type: 'audio/webm' });
                    recordedAudioFile = file;

                    displayRecordedAudio(file);
                    updateCreateVoiceButton();
                    showFileStatus('voiceStatus', `✅ Recording completed: ${file.name}`, 'success');

                    // Stop all tracks to release microphone
                    stream.getTracks().forEach(track => track.stop());
                };

                mediaRecorder.start();
                updateRecordingUI(true);
                startTimer();

            } catch (error) {
                console.error('Error accessing microphone:', error);
                showError('Could not access microphone. Please check permissions.');
            }
        }

        function stopRecording() {
            if (mediaRecorder && mediaRecorder.state === 'recording') {
                mediaRecorder.stop();
                updateRecordingUI(false);
                stopTimer();
            }
        }

        function updateRecordingUI(isRecording) {
            const button = document.getElementById('recordBtn');

            if (isRecording) {
                button.textContent = '⏹️ Stop Recording';
                button.classList.add('recording');
            } else {
                button.textContent = '🎙️ Record Voice';
                button.classList.remove('recording');
            }
        }

        function startTimer() {
            recordingTimer = setInterval(() => {
                const elapsed = Math.floor((Date.now() - recordingStartTime) / 1000);
                const minutes = Math.floor(elapsed / 60);
                const seconds = elapsed % 60;
                const timeStr = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
                document.getElementById('recordBtn').textContent = `⏹️ Recording ${timeStr}`;
            }, 1000);
        }

        function stopTimer() {
            if (recordingTimer) {
                clearInterval(recordingTimer);
                recordingTimer = null;
            }
        }

        // Create voice
        async function createVoice() {
            const voiceName = document.getElementById('voiceName').value.trim();

            if (!selectedProjectId) {
                showError('Please select a project first.');
                return;
            }

            if (!voiceName || !recordedAudioFile) {
                showError('Please enter a voice name and provide an audio file.');
                return;
            }

            try {
                showLoading();
                hideOutput();
                hideError();

                const formData = new FormData();
                formData.append('name', voiceName);
                formData.append('file', recordedAudioFile, recordedAudioFile.name);

                const response = await fetch(`${SERVER_BASE_URL}/api/projects/${selectedProjectId}/voices`, {
                    method: 'POST',
                    body: formData
                });

                if (response.ok) {
                    const result = await response.json();
                    hideLoading();

                    // Clear form
                    document.getElementById('voiceName').value = '';
                    document.getElementById('voiceFile').value = '';
                    recordedAudioFile = null;
                    document.getElementById('recordedAudio').innerHTML = '';
                    document.getElementById('voiceStatus').style.display = 'none';
                    updateCreateVoiceButton();

                    // Refresh voices list
                    loadVoices();

                    showFileStatus('voiceStatus', `✅ Voice "${voiceName}" created successfully!`, 'success');

                    // Auto-select the new voice
                    setTimeout(() => {
                        selectVoice(result.item.uuid, voiceName);
                    }, 1000);

                } else {
                    const error = await response.json();
                    throw new Error(error.error || 'Failed to create voice');
                }

            } catch (error) {
                console.error('Error creating voice:', error);
                hideLoading();
                showError(`Voice creation failed: ${error.message}`);
            }
        }

        // Delete voice
        async function deleteVoice(voiceId, voiceName) {
            if (!confirm(`Are you sure you want to delete the voice "${voiceName}"? This action cannot be undone.`)) {
                return;
            }

            try {
                const response = await fetch(`${SERVER_BASE_URL}/api/projects/${selectedProjectId}/voices/${voiceId}`, {
                    method: 'DELETE'
                });

                if (response.ok) {
                    loadVoices(); // Refresh the list

                    // Clear selection if deleted voice was selected
                    if (selectedVoiceId === voiceId) {
                        selectedVoiceId = null;
                        document.getElementById('selectedVoiceInfo').style.display = 'none';
                        updateGenerateButton();
                    }

                } else {
                    const error = await response.json();
                    showError(`Failed to delete voice: ${error.error}`);
                }

            } catch (error) {
                console.error('Error deleting voice:', error);
                showError(`Error deleting voice: ${error.message}`);
            }
        }

        // Generate speech
        async function generateSpeech() {
            const text = document.getElementById('ttsText').value.trim();
            const title = document.getElementById('clipTitle').value.trim();

            if (!selectedProjectId || !selectedVoiceId || !text) {
                showError('Please select a project, voice, and enter text to generate speech.');
                return;
            }

            try {
                showLoading();
                hideOutput();
                hideError();

                // First, create the clip
                const response = await fetch(`${SERVER_BASE_URL}/api/projects/${selectedProjectId}/clips`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        voice_uuid: selectedVoiceId,
                        text: text,
                        title: title || `Speech_${Date.now()}`
                    })
                });

                if (response.ok) {
                    const result = await response.json();
                    const clipId = result.item.uuid;

                    // Poll for completion and get download URL
                    pollClipStatus(selectedProjectId, clipId);

                } else {
                    const error = await response.json();
                    throw new Error(error.error || 'Failed to generate speech');
                }

            } catch (error) {
                console.error('Error generating speech:', error);
                hideLoading();
                showError(`Speech generation failed: ${error.message}`);
            }
        }

        // Poll clip status until ready
        async function pollClipStatus(projectId, clipId, attempts = 0) {
            const maxAttempts = 30; // 30 attempts = 1 minute max wait

            if (attempts >= maxAttempts) {
                hideLoading();
                showError('Speech generation timed out. Please try again.');
                return;
            }

            try {
                const response = await fetch(`${SERVER_BASE_URL}/api/projects/${projectId}/clips/${clipId}`);

                if (response.ok) {
                    const clip = await response.json();

                    if (clip.item.status === 'completed' && clip.item.audio_src) {
                        hideLoading();
                        showOutput(clip.item.audio_src);
                    } else if (clip.item.status === 'failed') {
                        hideLoading();
                        showError('Speech generation failed. Please try again.');
                    } else {
                        // Still processing, check again in 2 seconds
                        setTimeout(() => {
                            pollClipStatus(projectId, clipId, attempts + 1);
                        }, 2000);
                    }
                } else {
                    throw new Error('Failed to check clip status');
                }

            } catch (error) {
                console.error('Error checking clip status:', error);
                hideLoading();
                showError(`Error checking speech status: ${error.message}`);
            }
        }

        // Update button states
        function updateCreateProjectButton() {
            const projectName = document.getElementById('projectName').value.trim();
            document.getElementById('createProjectBtn').disabled = !projectName;
        }

        function updateCreateVoiceButton() {
            const voiceName = document.getElementById('voiceName').value.trim();
            const hasFile = recordedAudioFile !== null;
            const hasProject = selectedProjectId !== null;
            document.getElementById('createVoiceBtn').disabled = !(voiceName && hasFile && hasProject);
        }

        function updateGenerateButton() {
            const text = document.getElementById('ttsText').value.trim();
            const hasVoice = selectedVoiceId !== null;
            const hasProject = selectedProjectId !== null;
            document.getElementById('generateSpeechBtn').disabled = !(text && hasVoice && hasProject);
        }

        // Utility functions
        function showFileStatus(elementId, message, type) {
            const statusEl = document.getElementById(elementId);
            statusEl.textContent = message;
            statusEl.className = `file-status ${type}`;
            statusEl.style.display = message ? 'block' : 'none';
        }

        function showLoading() {
            document.getElementById('loading').classList.add('show');
        }

        function hideLoading() {
            document.getElementById('loading').classList.remove('show');
        }

        function showOutput(audioUrl) {
            const outputPlayer = document.getElementById('outputPlayer');
            outputPlayer.src = audioUrl;
            outputPlayer.audioUrl = audioUrl; // Store for download
            document.getElementById('outputContainer').style.display = 'block';
        }

        function hideOutput() {
            document.getElementById('outputContainer').style.display = 'none';
        }

        function showError(message) {
            document.getElementById('errorMessage').textContent = message;
            document.getElementById('errorContainer').style.display = 'block';
        }

        function hideError() {
            document.getElementById('errorContainer').style.display = 'none';
        }

        async function downloadAudio() {
            const outputPlayer = document.getElementById('outputPlayer');
            if (outputPlayer.audioUrl) {
                try {
                    const response = await fetch(outputPlayer.audioUrl);
                    const blob = await response.blob();
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `resemble_speech_${Date.now()}.mp3`;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    URL.revokeObjectURL(url);
                } catch (error) {
                    console.error('Error downloading audio:', error);
                    showError('Failed to download audio file.');
                }
            }
        }
    </script>
</body>
</html>
