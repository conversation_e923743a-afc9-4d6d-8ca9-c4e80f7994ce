import React, { useState } from 'react';
import { ChevronDown, ChevronUp, ExternalLink, Code, Cpu, Globe, Building2, Star, Zap, Shield, Clock } from 'lucide-react';

const AudioWorkflowTable = () => {
  const [expandedRows, setExpandedRows] = useState(new Set());
  const [activeTab, setActiveTab] = useState({});

  const toggleRow = (index) => {
    const newExpanded = new Set(expandedRows);
    if (newExpanded.has(index)) {
      newExpanded.delete(index);
    } else {
      newExpanded.add(index);
    }
    setExpandedRows(newExpanded);
  };

  const setTab = (rowIndex, tabName) => {
    setActiveTab(prev => ({ ...prev, [rowIndex]: tabName }));
  };

  const workflowData = [
    {
      step: "Input Audio",
      description: "Audio capture and input handling",
      skipDetailed: true,
      techniques: [
        "PCM (Pulse Code Modulation)",
        "WAV, MP3, FLAC formats",
        "Real-time audio streaming",
        "Microphone capture"
      ],
      libraries: {
        python: ["pyaudio", "soundfile", "librosa", "pydub"],
        javascript: ["Web Audio API", "MediaRecorder API", "tone.js"],
        cpp: ["PortAudio", "JUCE", "RtAudio", "FMOD"],
        other: ["FFmpeg", "SoX", "GStreamer"]
      },
      apis: [
        { name: "Web Audio API", type: "Browser", url: "https://developer.mozilla.org/en-US/docs/Web/API/Web_Audio_API", pricing: "Free" },
        { name: "MediaDevices API", type: "Browser", url: "https://developer.mozilla.org/en-US/docs/Web/API/MediaDevices", pricing: "Free" }
      ]
    },
    {
      step: "Preprocessing",
      icon: "⚙️",
      description: "Convert to mono, normalize, resample",
      techniques: [
        "Sample Rate Conversion (SRC)", "Bit Depth Conversion", "Audio Normalization", "DC Offset Removal",
        "Pre-emphasis Filtering", "Windowing (Hamming, Hanning)", "Zero Padding", "Audio Trimming",
        "Channel Mixing", "Gain Control", "Dynamic Range Compression", "Anti-aliasing Filtering",
        "Audio Segmentation", "Frame Blocking", "Overlap-Add Processing"
      ],
      realWorldExamples: [
        {
          company: "Spotify",
          usage: "Audio normalization using Replay Gain and LUFS standards for consistent playback levels across all tracks",
          tech: "Custom C++ audio processing pipeline with LUFS measurement"
        },
        {
          company: "Zoom",
          usage: "Real-time sample rate conversion and bit depth adjustment for cross-platform compatibility",
          tech: "WebRTC audio processing with custom preprocessing modules"
        },
        {
          company: "Audacity",
          usage: "Multi-format audio conversion and normalization for podcast production",
          tech: "FFmpeg integration with custom preprocessing algorithms"
        },
        {
          company: "Discord",
          usage: "Audio preprocessing for voice chat optimization including DC removal and gain control",
          tech: "Opus codec with custom preprocessing pipeline"
        },
        {
          company: "Adobe Audition",
          usage: "Professional audio preprocessing with spectral editing and advanced normalization",
          tech: "Proprietary DSP algorithms with GPU acceleration"
        }
      ],
      libraries: {
        python: ["librosa", "scipy.signal", "soundfile", "numpy", "pydub", "audioread", "resampy", "pyrubberband"],
        javascript: ["Web Audio API", "audio-buffer-utils", "node-ffmpeg", "fluent-ffmpeg", "audio-context-polyfill"],
        cpp: ["libsamplerate", "JUCE", "Eigen", "FFTW", "PortAudio", "libsndfile", "rubberband"],
        other: ["FFmpeg", "SoX", "libresample", "LAME", "FLAC", "Opus"]
      },
      apis: [
        { 
          name: "FFmpeg Cloud API", 
          type: "Cloud", 
          url: "https://api.bannerbear.com/", 
          pricing: "Pay-per-use",
          features: ["Format conversion", "Sample rate conversion", "Normalization"],
          reliability: "99.9%"
        },
        { 
          name: "Cloudinary Audio API", 
          type: "Cloud", 
          url: "https://cloudinary.com/documentation/audio_transformations", 
          pricing: "Freemium",
          features: ["Audio transformations", "Format conversion", "Quality optimization"],
          reliability: "99.99%"
        },
        { 
          name: "AWS Elemental MediaConvert", 
          type: "Cloud", 
          url: "https://aws.amazon.com/mediaconvert/", 
          pricing: "$0.0075/minute",
          features: ["Batch processing", "Format conversion", "Audio preprocessing"],
          reliability: "99.9%"
        }
      ]
    },
    {
      step: "Noise Reduction",
      icon: "🔇",
      description: "Spectral gating, DNN models for denoising",
      techniques: [
        "Spectral Subtraction", "Wiener Filtering", "RNNoise (Recurrent Neural Network)", "Spectral Gating",
        "Wavelet Denoising", "Deep Learning (U-Net, WaveNet)", "Kalman Filtering", "Adaptive Filtering",
        "Multi-band Compressor", "Noise Gate", "DeepFilterNet", "Facebook Denoiser", "DTLN",
        "Conv-TasNet", "Dual-Path RNN", "Time-Frequency Masking", "Beamforming", "Coherence-based Filtering"
      ],
      realWorldExamples: [
        {
          company: "NVIDIA RTX Voice",
          usage: "Real-time AI-powered noise suppression for streaming and video calls using deep learning",
          tech: "Custom DNN models optimized for RTX GPUs with real-time inference"
        },
        {
          company: "Krisp",
          usage: "AI-powered noise cancellation for business calls supporting 800+ apps",
          tech: "Deep learning models with edge computing, processes 15M+ minutes daily"
        },
        {
          company: "Zoom",
          usage: "Background noise suppression in video conferencing using multiple algorithms",
          tech: "Hybrid approach: spectral subtraction + ML models, WebRTC integration"
        },
        {
          company: "Dolby Atmos",
          usage: "Professional noise reduction for cinema and streaming content",
          tech: "Advanced spectral processing with psychoacoustic modeling"
        },
        {
          company: "Apple AirPods Pro",
          usage: "Active noise cancellation using adaptive algorithms and dual microphones",
          tech: "Custom H1/H2 chip with real-time adaptive filtering"
        },
        {
          company: "Google Meet",
          usage: "ML-based noise cancellation that removes background sounds while preserving speech",
          tech: "RNN-based models trained on diverse noise datasets"
        }
      ],
      libraries: {
        python: ["noisereduce", "librosa", "tensorflow", "pytorch", "scipy", "torch-audiomentations", "asteroid", "speechbrain"],
        javascript: ["rnnoise-wasm", "Web Audio API", "@tensorflow/tfjs", "tone.js", "audio-worklet"],
        cpp: ["RNNoise", "JUCE", "Speex", "WebRTC", "Intel IPP", "FFTW", "Eigen"],
        other: ["Audacity", "Adobe Audition SDK", "iZotope RX", "Steinberg VST", "LADSPA"]
      },
      apis: [
        { 
          name: "Krisp API", 
          type: "Cloud", 
          url: "https://krisp.ai/", 
          pricing: "$5/user/month",
          features: ["Real-time noise cancellation", "SDK integration", "Multi-platform"],
          reliability: "99.5%"
        },
        { 
          name: "Dolby.io Media API", 
          type: "Cloud", 
          url: "https://dolby.io/", 
          pricing: "Pay-per-use",
          features: ["Professional noise reduction", "Audio enhancement", "Real-time processing"],
          reliability: "99.9%"
        },
        { 
          name: "Speechmatics Noise Reduction", 
          type: "Cloud", 
          url: "https://www.speechmatics.com/", 
          pricing: "$0.30/hour",
          features: ["AI-powered denoising", "Batch processing", "Multiple languages"],
          reliability: "99.7%"
        },
        { 
          name: "WebRTC Audio Processing", 
          type: "Open Source", 
          url: "https://webrtc.googlesource.com/src/", 
          pricing: "Free",
          features: ["Noise suppression", "Echo cancellation", "Gain control"],
          reliability: "Community"
        }
      ]
    },
    {
      step: "Voice Activity Detection (VAD)",
      icon: "🎤",
      description: "Detect speech vs silence/noise",
      techniques: [
        "Energy-based VAD", "Zero Crossing Rate", "Spectral Centroid", "Machine Learning VAD",
        "WebRTC VAD", "Deep Learning VAD", "Pitch-based Detection", "Formant Analysis",
        "Harmonic-to-Noise Ratio", "Spectral Rolloff", "LSTM-based VAD", "CNN-based VAD",
        "Markov Model VAD", "GMM-based VAD", "SVM Classification", "Multi-feature Fusion",
        "Adaptive Thresholding", "Temporal Smoothing"
      ],
      realWorldExamples: [
        {
          company: "Amazon Alexa",
          usage: "Wake word detection and continuous voice activity monitoring with edge processing",
          tech: "Multi-stage DNN models with keyword spotting and VAD fusion"
        },
        {
          company: "Google Assistant",
          usage: "Real-time voice activity detection for hands-free interaction and hotword detection",
          tech: "Lightweight neural networks optimized for mobile devices"
        },
        {
          company: "Microsoft Teams",
          usage: "Smart muting and automatic gain control based on voice activity detection",
          tech: "WebRTC VAD enhanced with custom ML models"
        },
        {
          company: "Otter.ai",
          usage: "Meeting transcription with intelligent speaker segmentation using VAD",
          tech: "Transformer-based models with temporal context awareness"
        },
        {
          company: "Spotify Car Thing",
          usage: "Voice command detection in noisy vehicle environments",
          tech: "Robust VAD with noise adaptation and beam-forming"
        },
        {
          company: "Clubhouse",
          usage: "Real-time audio streaming with intelligent silence detection for bandwidth optimization",
          tech: "Custom VAD algorithms with network-aware processing"
        }
      ],
      libraries: {
        python: ["webrtcvad", "py-webrtcvad", "librosa", "tensorflow", "torch", "speechbrain", "pyannote.audio", "silero-vad"],
        javascript: ["vad", "Web Audio API", "webrtc-vad", "@tensorflow/tfjs", "voice-activity-detection"],
        cpp: ["WebRTC", "JUCE", "Speex", "Kaldi", "OpenCV", "Eigen"],
        other: ["Kaldi", "Praat", "ELAN", "HTK", "Julius"]
      },
      apis: [
        { 
          name: "Google Cloud Speech-to-Text VAD", 
          type: "Cloud", 
          url: "https://cloud.google.com/speech-to-text/docs/vad", 
          pricing: "$0.024/15sec",
          features: ["Automatic punctuation", "Speaker diarization", "Real-time streaming"],
          reliability: "99.9%"
        },
        { 
          name: "Azure Cognitive Services Speech", 
          type: "Cloud", 
          url: "https://azure.microsoft.com/en-us/services/cognitive-services/speech-services/", 
          pricing: "$1/audio hour",
          features: ["Voice activity detection", "Custom models", "Batch processing"],
          reliability: "99.9%"
        },
        { 
          name: "AssemblyAI VAD", 
          type: "Cloud", 
          url: "https://www.assemblyai.com/", 
          pricing: "$0.00037/second",
          features: ["Real-time VAD", "Speaker detection", "Custom vocabulary"],
          reliability: "99.8%"
        },
        { 
          name: "Picovoice Cobra", 
          type: "Edge", 
          url: "https://picovoice.ai/platform/cobra/", 
          pricing: "$0.01/query",
          features: ["On-device VAD", "Cross-platform", "Low latency"],
          reliability: "99.5%"
        }
      ]
    },
    {
      step: "Speech Enhancement",
      icon: "✨",
      description: "Improve clarity and quality",
      techniques: [
        "Dynamic Range Compression", "Equalization", "Noise Gate", "Spectral Enhancement",
        "Speech Dereverberation", "Bandwidth Extension", "Harmonic Enhancement", "Formant Correction",
        "Pitch Shifting", "Time Stretching", "Vocal Isolation", "De-essing", "Breath Removal",
        "Click and Pop Removal", "Hum Removal", "Spectral Repair", "Multi-band Processing",
        "Psychoacoustic Enhancement", "Stereo Width Enhancement", "Loudness Maximization"
      ],
      realWorldExamples: [
        {
          company: "iZotope RX",
          usage: "Professional audio repair and enhancement for film, music, and podcast post-production",
          tech: "Advanced spectral editing with ML-powered audio repair algorithms"
        },
        {
          company: "Adobe Audition",
          usage: "Podcast and broadcast audio enhancement with spectral frequency display",
          tech: "Real-time DSP with GPU acceleration and machine learning enhancement"
        },
        {
          company: "Hindenburg Pro",
          usage: "Journalist and radio production with automatic leveling and EQ",
          tech: "Broadcast-specific algorithms with automatic speech enhancement"
        },
        {
          company: "Auphonic",
          usage: "Automated podcast post-production with intelligent leveling and noise reduction",
          tech: "Cloud-based audio processing with EBU R128 loudness standards"
        },
        {
          company: "Acon Digital Restoration Suite",
          usage: "Audio restoration for historical recordings and damaged audio files",
          tech: "Advanced spectral processing with click removal and noise reduction"
        },
        {
          company: "Cedar Audio",
          usage: "Professional audio restoration for broadcast and forensic applications",
          tech: "Hardware-accelerated DSP with proprietary enhancement algorithms"
        }
      ],
      libraries: {
        python: ["librosa", "scipy.signal", "pedalboard", "tensorflow", "torch", "essentia", "madmom", "pyroomacoustics"],
        javascript: ["Web Audio API", "tone.js", "audio-effects", "standardized-audio-context", "web-audio-beat-detector"],
        cpp: ["JUCE", "iPlug2", "Maximilian", "FMOD", "Wwise", "OpenAL", "Steam Audio"],
        other: ["LADSPA", "VST SDK", "Audio Units", "AAX SDK", "RTAS", "DirectSound"]
      },
      apis: [
        { 
          name: "Auphonic API", 
          type: "Cloud", 
          url: "https://auphonic.com/api/", 
          pricing: "€0.18/hour",
          features: ["Automatic leveling", "Noise reduction", "Speech enhancement"],
          reliability: "99.8%"
        },
        { 
          name: "Dolby.io Media Processing", 
          type: "Cloud", 
          url: "https://dolby.io/products/media-processing/", 
          pricing: "Pay-per-use",
          features: ["Professional enhancement", "Loudness control", "Dynamic EQ"],
          reliability: "99.9%"
        },
        { 
          name: "AWS Elemental MediaLive", 
          type: "Cloud", 
          url: "https://aws.amazon.com/medialive/", 
          pricing: "$0.45/hour",
          features: ["Live audio processing", "Real-time enhancement", "Broadcast quality"],
          reliability: "99.9%"
        },
        { 
          name: "Wwise Audio Engine", 
          type: "SDK", 
          url: "https://www.audiokinetic.com/products/wwise/", 
          pricing: "Free for small projects",
          features: ["Real-time audio processing", "3D audio", "Interactive enhancement"],
          reliability: "Industry standard"
        }
      ]
    },
    {
      step: "Feature Extraction",
      icon: "📊",
      description: "MFCC, log-mel spectrograms",
      techniques: [
        "MFCC (Mel-Frequency Cepstral Coefficients)", "Log-Mel Spectrograms", "Chroma Features", "Spectral Contrast",
        "Tonnetz", "Zero Crossing Rate", "Pitch Features (F0)", "Spectral Centroid", "Spectral Bandwidth",
        "Spectral Rolloff", "Root Mean Square Energy", "Spectral Flatness", "Delta and Delta-Delta Features",
        "Linear Predictive Coding (LPC)", "Perceptual Linear Prediction (PLP)", "Gammatone Features",
        "Constant-Q Transform", "Wavelet Features", "Prosodic Features", "Voice Quality Features"
      ],
      realWorldExamples: [
        {
          company: "Shazam",
          usage: "Audio fingerprinting using spectral features for music identification from short clips",
          tech: "Custom spectrogram analysis with robust hash generation from peak frequencies"
        },
        {
          company: "SoundHound",
          usage: "Music recognition and humming detection using advanced feature extraction",
          tech: "Multi-resolution spectral analysis with pitch tracking and harmonic features"
        },
        {
          company: "Spotify",
          usage: "Music recommendation system using audio features for similarity matching",
          tech: "Large-scale MFCC extraction with CNN-based feature learning"
        },
        {
          company: "Google Speech Recognition",
          usage: "Feature extraction pipeline for multilingual speech recognition systems",
          tech: "Log-mel spectrograms with SpecAugment and neural feature enhancement"
        },
        {
          company: "Siri (Apple)",
          usage: "Voice command recognition using optimized feature extraction for mobile devices",
          tech: "Lightweight MFCC computation with on-device neural network acceleration"
        },
        {
          company: "Gracenote",
          usage: "Music metadata and mood detection using comprehensive audio feature analysis",
          tech: "Multi-dimensional feature vectors with tempo, key, and timbre analysis"
        }
      ],
      libraries: {
        python: ["librosa", "python_speech_features", "torchaudio", "speechpy", "essentia", "madmom", "mir_eval", "pyannote.audio"],
        javascript: ["meyda", "Web Audio API", "ml-matrix", "tone.js", "audio-features", "web-audio-peak-meter"],
        cpp: ["Kaldi", "JUCE", "Aquila", "YAAFE", "Essentia", "aubio", "CLAM", "Marsyas"],
        other: ["openSMILE", "Praat", "Essentia", "MIRtoolbox", "Sonic Annotator", "jAudio"]
      },
      apis: [
        { 
          name: "Spotify Web API Audio Features", 
          type: "Cloud", 
          url: "https://developer.spotify.com/documentation/web-api/reference/get-audio-features", 
          pricing: "Free with limits",
          features: ["Tempo", "Key", "Loudness", "Danceability", "Energy"],
          reliability: "99.9%"
        },
        { 
          name: "AcousticBrainz API", 
          type: "Cloud", 
          url: "https://acousticbrainz.org/", 
          pricing: "Free",
          features: ["Low-level features", "High-level features", "Music analysis"],
          reliability: "Community"
        },
        { 
          name: "Echonest API (Legacy)", 
          type: "Cloud", 
          url: "https://developer.echonest.com/", 
          pricing: "Discontinued",
          features: ["Audio analysis", "Music intelligence", "Feature extraction"],
          reliability: "N/A"
        },
        { 
          name: "Google Cloud Video Intelligence", 
          type: "Cloud", 
          url: "https://cloud.google.com/video-intelligence", 
          pricing: "$0.10/minute",
          features: ["Audio feature detection", "Speech transcription", "Sound classification"],
          reliability: "99.9%"
        }
      ]
    },
    {
      step: "Speech Recognition (ASR)",
      icon: "🗣️",
      description: "Whisper, Google STT, etc.",
      techniques: [
        "Deep Neural Networks (DNN)", "Recurrent Neural Networks (RNN/LSTM)", "Transformer Models", 
        "Connectionist Temporal Classification (CTC)", "Attention Mechanisms", "End-to-End Models",
        "Wav2Vec 2.0", "Conformer Architecture", "Squeezeformer", "ContextNet", "QuartzNet",
        "Jasper", "CitriNet", "FastConformer", "Streaming Transformers", "Neural Transducers",
        "Federated Learning", "Multi-task Learning", "Self-supervised Learning", "Knowledge Distillation"
      ],
      realWorldExamples: [
        {
          company: "OpenAI Whisper",
          usage: "Multilingual speech recognition supporting 99+ languages with high accuracy",
          tech: "Transformer-based encoder-decoder with 680M parameters, trained on 680k hours"
        },
        {
          company: "Google Speech-to-Text",
          usage: "Real-time transcription for Google Meet, YouTube captions, and Google Assistant",
          tech: "RNN-Transducer models with streaming capability and on-device optimization"
        },
        {
          company: "Amazon Transcribe",
          usage: "Call center analytics, media transcription, and Alexa voice processing",
          tech: "Transformer-based models with custom vocabulary and speaker diarization"
        },
        {
          company: "Microsoft Azure Speech",
          usage: "Teams meetings, Office dictation, and Xbox voice commands",
          tech: "Neural network models with custom speech and pronunciation assessment"
        },
        {
          company: "Rev.ai",
          usage: "Professional transcription services for media, legal, and business applications",
          tech: "Hybrid human-AI approach with custom ASR models and quality assurance"
        },
        {
          company: "Otter.ai",
          usage: "Meeting transcription with speaker identification and real-time collaboration",
          tech: "Custom transformer models with conversation understanding and summarization"
        },
        {
          company: "AssemblyAI",
          usage: "Developer-focused ASR API with advanced features like sentiment analysis",
          tech: "State-of-the-art transformer models with specialized domain adaptation"
        }
      ],
      libraries: {
        python: ["whisper", "speech_recognition", "deepspeech", "wav2vec2", "transformers", "speechbrain", "espnet", "kaldi"],
        javascript: ["speechrecognition-polyfill", "annyang", "Web Speech API", "@tensorflow/tfjs", "microsoft-cognitiveservices-speech-sdk"],
        cpp: ["Kaldi", "wav2letter++", "DeepSpeech", "Vosk", "PocketSphinx", "Julius"],
        other: ["CMU Sphinx", "Julius", "HTK", "RWTH ASR", "ESPnet", "SpeechBrain"]
      },
      apis: [
        { 
          name: "OpenAI Whisper API", 
          type: "Cloud", 
          url: "https://platform.openai.com/docs/guides/speech-to-text", 
          pricing: "$0.006/minute",
          features: ["99+ languages", "Timestamps", "Word-level confidence"],
          reliability: "99.9%"
        },
        { 
          name: "Google Cloud Speech-to-Text", 
          type: "Cloud", 
          url: "https://cloud.google.com/speech-to-text", 
          pricing: "$0.024/15sec",
          features: ["Real-time streaming", "Custom models", "Speaker diarization"],
          reliability: "99.9%"
        },
        { 
          name: "Amazon Transcribe", 
          type: "Cloud", 
          url: "https://aws.amazon.com/transcribe/", 
          pricing: "$0.024/minute",
          features: ["Custom vocabulary", "Content filtering", "Call analytics"],
          reliability: "99.9%"
        },
        { 
          name: "Microsoft Speech Services", 
          type: "Cloud", 
          url: "https://azure.microsoft.com/en-us/services/cognitive-services/speech-to-text/", 
          pricing: "$1/audio hour",
          features: ["Custom speech", "Pronunciation assessment", "Batch transcription"],
          reliability: "99.9%"
        },
        { 
          name: "AssemblyAI", 
          type: "Cloud", 
          url: "https://www.assemblyai.com/", 
          pricing: "$0.00037/second",
          features: ["Speaker labels", "Sentiment analysis", "Entity detection"],
          reliability: "99.8%"
        },
        { 
          name: "Rev.ai", 
          type: "Cloud", 
          url: "https://www.rev.ai/", 
          pricing: "$0.02/minute",
          features: ["Human transcription", "Custom vocabulary", "Topic detection"],
          reliability: "99.7%"
        },
        { 
          name: "Web Speech API", 
          type: "Browser", 
          url: "https://developer.mozilla.org/en-US/docs/Web/API/Web_Speech_API", 
          pricing: "Free",
          features: ["Real-time recognition", "Continuous listening", "Language detection"],
          reliability: "Browser dependent"
        }
      ]
    }
  ];

  const getBadgeColor = (type) => {
    const colors = {
      'Browser': 'bg-blue-100 text-blue-800 border-blue-200',
      'Cloud': 'bg-green-100 text-green-800 border-green-200',
      'Cross-platform': 'bg-purple-100 text-purple-800 border-purple-200',
      'SDK': 'bg-orange-100 text-orange-800 border-orange-200',
      'Edge': 'bg-indigo-100 text-indigo-800 border-indigo-200',
      'Open Source': 'bg-gray-100 text-gray-800 border-gray-200'
    };
    return colors[type] || 'bg-gray-100 text-gray-800 border-gray-200';
  };

  const getReliabilityColor = (reliability) => {
    if (reliability.includes('99.9')) return 'text-green-600';
    if (reliability.includes('99')) return 'text-blue-600';
    return 'text-gray-600';
  };

  return (
    <div className="max-w-full mx-auto p-6 bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
      <div className="mb-8 text-center">
        <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-4">
          Audio Processing Workflow
        </h1>
        <p className="text-xl text-gray-600 max-w-3xl mx-auto">
          Comprehensive guide to techniques, libraries, real-world implementations, and APIs for professional audio processing
        </p>
      </div>

      <div className="space-y-6">
        {workflowData.map((row, index) => (
          <div key={index} className="bg-white rounded-2xl shadow-lg border border-gray-200 overflow-hidden hover:shadow-xl transition-all duration-300">
            {/* Header Row */}
            <div className="p-6 bg-gradient-to-r from-gray-50 to-blue-50 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="text-3xl">{row.icon || "🎵"}</div>
                  <div>
                    <h3 className="text-2xl font-bold text-gray-900">{row.step}</h3>
                    <p className="text-gray-600 mt-1">{row.description}</p>
                  </div>
                </div>
                <div className="flex items-center space-x-4">
                  <div className="flex flex-wrap gap-2">
                    {row.techniques.slice(0, 3).map((technique, i) => (
                      <span key={i} className="inline-block bg-blue-100 text-blue-800 text-sm px-3 py-1 rounded-full font-medium">
                        {technique}
                      </span>
                    ))}
                    {row.techniques.length > 3 && (
                      <span className="inline-block bg-gray-100 text-gray-600 text-sm px-3 py-1 rounded-full font-medium">
                        +{row.techniques.length - 3} more
                      </span>
                    )}
                  </div>
                  {!row.skipDetailed && (
                    <button
                      onClick={() => toggleRow(index)}
                      className="flex items-center px-6 py-3 bg-blue-600 text-white rounded-xl hover:bg-blue-700 transition-colors font-medium shadow-md"
                    >
                      {expandedRows.has(index) ? (
                        <>Hide Details <ChevronUp className="ml-2 w-5 h-5" /></>
                      ) : (
                        <>Show Details <ChevronDown className="ml-2 w-5 h-5" /></>
                      )}
                    </button>
                  )}
                </div>
              </div>
            </div>
            
            {/* Expanded Content */}
            {expandedRows.has(index) && !row.skipDetailed && (
              <div className="p-6">
                {/* Tab Navigation */}
                <div className="flex space-x-1 mb-6 bg-gray-100 p-1 rounded-xl">
                  {['techniques', 'examples', 'libraries', 'apis'].map(tab => (
                    <button
                      key={tab}
                      onClick={() => setTab(index, tab)}
                      className={`px-4 py-2 rounded-lg font-medium capitalize transition-colors ${
                        (activeTab[index] || 'techniques') === tab
                          ? 'bg-white text-blue-600 shadow-sm'
                          : 'text-gray-600 hover:text-gray-900'
                      }`}
                    >
                      {tab === 'examples' ? 'Real World' : tab}
                    </button>
                  ))}
                </div>

                {/* Tab Content */}
                <div className="space-y-6">
                  {/* Techniques Tab */}
                  {(activeTab[index] || 'techniques') === 'techniques' && (
                    <div>
                      <h4 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                        <Cpu className="mr-3 w-6 h-6 text-blue-600" />
                        Advanced Techniques & Methods
                      </h4>
                      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
                        {row.techniques.map((technique, i) => (
                          <div key={i} className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 px-4 py-3 rounded-lg hover:shadow-md transition-shadow">
                            <span className="text-blue-800 font-medium text-sm">{technique}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Real World Examples Tab */}
                  {(activeTab[index] || 'techniques') === 'examples' && (
                    <div>
                      <h4 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                        <Building2 className="mr-3 w-6 h-6 text-green-600" />
                        Real-World Industry Applications
                      </h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        {row.realWorldExamples.map((example, i) => (
                          <div key={i} className="bg-gradient-to-br from-green-50 to-emerald-50 border border-green-200 p-6 rounded-xl hover:shadow-lg transition-shadow">
                            <div className="flex items-center mb-3">
                              <Building2 className="w-5 h-5 text-green-600 mr-2" />
                              <h5 className="font-bold text-lg text-gray-900">{example.company}</h5>
                            </div>
                            <p className="text-gray-700 mb-3 leading-relaxed">{example.usage}</p>
                            <div className="bg-white bg-opacity-60 p-3 rounded-lg">
                              <p className="text-sm text-gray-600 font-medium">Technology Stack:</p>
                              <p className="text-sm text-gray-800">{example.tech}</p>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Libraries Tab */}
                  {(activeTab[index] || 'techniques') === 'libraries' && (
                    <div>
                      <h4 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                        <Code className="mr-3 w-6 h-6 text-purple-600" />
                        Popular Libraries & Frameworks
                      </h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                        {Object.entries(row.libraries).map(([lang, libs]) => (
                          <div key={lang} className="bg-gradient-to-br from-purple-50 to-indigo-50 border border-purple-200 p-6 rounded-xl hover:shadow-lg transition-shadow">
                            <h5 className="font-bold text-lg text-gray-900 mb-4 capitalize flex items-center">
                              <Code className="w-4 h-4 mr-2 text-purple-600" />
                              {lang}
                            </h5>
                            <div className="space-y-2">
                              {libs.map((lib, i) => (
                                <div key={i} className="bg-white bg-opacity-60 px-3 py-2 rounded-lg">
                                  <span className="text-sm font-medium text-gray-800">{lib}</span>
                                </div>
                              ))}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* APIs Tab */}
                  {(activeTab[index] || 'techniques') === 'apis' && (
                    <div>
                      <h4 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                        <Globe className="mr-3 w-6 h-6 text-indigo-600" />
                        Available APIs & Services
                      </h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        {row.apis.map((api, i) => (
                          <div key={i} className="bg-gradient-to-br from-indigo-50 to-blue-50 border border-indigo-200 p-6 rounded-xl hover:shadow-lg transition-shadow">
                            <div className="flex items-start justify-between mb-3">
                              <h6 className="font-bold text-lg text-gray-900 flex-1">{api.name}</h6>
                              <span className={`px-3 py-1 rounded-full text-xs font-medium border ${getBadgeColor(api.type)}`}>
                                {api.type}
                              </span>
                            </div>
                            
                            {api.pricing && (
                              <div className="flex items-center mb-2">
                                <Zap className="w-4 h-4 text-yellow-500 mr-2" />
                                <span className="text-sm font-medium text-gray-700">Pricing: {api.pricing}</span>
                              </div>
                            )}
                            
                            {api.reliability && (
                              <div className="flex items-center mb-3">
                                <Shield className={`w-4 h-4 mr-2 ${getReliabilityColor(api.reliability)}`} />
                                <span className={`text-sm font-medium ${getReliabilityColor(api.reliability)}`}>
                                  Reliability: {api.reliability}
                                </span>
                              </div>
                            )}

                            {api.features && (
                              <div className="mb-4">
                                <p className="text-sm font-medium text-gray-700 mb-2">Key Features:</p>
                                <div className="flex flex-wrap gap-1">
                                  {api.features.map((feature, fi) => (
                                    <span key={fi} className="bg-white bg-opacity-60 text-xs px-2 py-1 rounded">
                                      {feature}
                                    </span>
                                  ))}
                                </div>
                              </div>
                            )}
                            
                            <a 
                              href={api.url} 
                              target="_blank" 
                              rel="noopener noreferrer"
                              className="inline-flex items-center text-blue-600 hover:text-blue-800 font-medium transition-colors"
                            >
                              View Documentation <ExternalLink className="ml-2 w-4 h-4" />
                            </a>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Footer */}
      <div className="mt-12 p-8 bg-white rounded-2xl shadow-lg border border-gray-200">
        <h3 className="text-2xl font-bold text-gray-900 mb-4 flex items-center">
          <Star className="mr-3 w-6 h-6 text-yellow-500" />
          Usage Guidelines & Best Practices
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div className="bg-blue-50 p-6 rounded-xl border border-blue-200">
            <Clock className="w-8 h-8 text-blue-600 mb-3" />
            <h4 className="font-semibold text-blue-900 mb-2">Performance Considerations</h4>
            <ul className="text-blue-800 text-sm space-y-1">
              <li>• Cloud APIs offer higher accuracy but add latency</li>
              <li>• Local processing provides real-time performance</li>
              <li>• Consider batch processing for large datasets</li>
            </ul>
          </div>
          
          <div className="bg-green-50 p-6 rounded-xl border border-green-200">
            <Shield className="w-8 h-8 text-green-600 mb-3" />
            <h4 className="font-semibold text-green-900 mb-2">Privacy & Security</h4>
            <ul className="text-green-800 text-sm space-y-1">
              <li>• On-device processing for sensitive data</li>
              <li>• Review data retention policies for cloud APIs</li>
              <li>• Consider GDPR compliance requirements</li>
            </ul>
          </div>
          
          <div className="bg-purple-50 p-6 rounded-xl border border-purple-200">
            <Zap className="w-8 h-8 text-purple-600 mb-3" />
            <h4 className="font-semibold text-purple-900 mb-2">Cost Optimization</h4>
            <ul className="text-purple-800 text-sm space-y-1">
              <li>• Compare pricing models across providers</li>
              <li>• Use preprocessing to reduce API calls</li>
              <li>• Consider hybrid local/cloud approaches</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AudioWorkflowTable;