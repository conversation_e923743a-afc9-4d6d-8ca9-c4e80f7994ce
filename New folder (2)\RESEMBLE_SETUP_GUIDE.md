# Resemble.ai Setup Guide

## Quick Start

### Step 1: Get Your Resemble.ai API Key

1. **Sign up/Login** to Resemble.ai:
   - Go to [resemble.ai](https://resemble.ai/)
   - Create an account or log in

2. **Get your API Key**:
   - Go to [Account Settings](https://app.resemble.ai/account)
   - Copy your API token

### Step 2: Configure the Server

1. **Open** `resemble_server.js` in a text editor
2. **Find** this line (around line 12):
   ```javascript
   const RESEMBLE_API_KEY = 'your_resemble_api_key_here';
   ```
3. **Replace** `'your_resemble_api_key_here'` with your actual API key:
   ```javascript
   const RESEMBLE_API_KEY = 'your-actual-api-key-here';
   ```
4. **Save** the file

### Step 3: Start the Server

```bash
node resemble_server.js
```

### Step 4: Open the App

Open your browser and go to: **http://localhost:3002**

## Features Overview

### 📁 Project Management
- Organize voice models into projects
- Create, view, and delete projects
- Project-based voice organization

### 🎤 Voice Cloning
- Upload audio samples (10 seconds - 10 minutes)
- Record directly in the browser
- Fast voice model creation (1-2 minutes)
- High-quality voice synthesis

### 🔊 Real-time Speech Generation
- Generate speech with any voice
- Fast synthesis (usually < 10 seconds)
- High-quality MP3 output
- Download generated audio

## Step-by-Step Workflow

### 1. Create Your First Project
1. Enter a project name (e.g., "My Voice Models")
2. Add a description (optional)
3. Click "Create Project"
4. Select the project from the list

### 2. Create Your First Voice
1. Make sure a project is selected
2. Enter a voice name (e.g., "John's Voice")
3. Upload an audio file OR record directly
4. Click "Create Voice"
5. Wait 1-2 minutes for processing

### 3. Generate Speech
1. Select your created voice
2. Enter text to convert to speech
3. Add a clip title (optional)
4. Click "Generate Speech"
5. Download the result

## Audio Requirements

### Best Practices
- **Duration**: 10 seconds to 10 minutes
- **Quality**: Clear, noise-free recordings
- **Format**: WAV, MP3, FLAC preferred
- **Content**: Natural conversational speech
- **Speaker**: Single person only

### Recording Tips
1. Use a quiet environment
2. Speak naturally and clearly
3. Use a good quality microphone
4. Avoid background music or noise
5. Don't over-compress the audio

## Troubleshooting

### Common Issues

**"API Error 401"**
- Check your API key is correct
- Ensure your Resemble.ai account is active

**"Voice creation failed"**
- Check audio file quality and format
- Ensure file size is reasonable (< 100MB)
- Try with different audio samples

**"No projects found"**
- Create your first project using the form
- Refresh the projects list

**"Cannot access microphone"**
- Allow microphone permissions in browser
- Use HTTPS in production

### Getting Help

- Check the browser console for detailed error messages
- Verify your internet connection
- Ensure the server is running on port 3002

## Resemble.ai Advantages

### ✅ **Strengths**
- **Fast Setup**: Voice models ready in 1-2 minutes
- **Project Organization**: Clean project-based structure
- **Real-time Generation**: Very fast speech synthesis
- **High Quality**: Professional-grade voice cloning
- **API Design**: Well-structured RESTful API

### 🔄 **Workflow Benefits**
- **Organized**: Projects keep voices organized
- **Scalable**: Handle multiple voices efficiently
- **Fast**: Quick voice creation and speech generation
- **Professional**: Enterprise-ready features

## Pricing

- **Pay-per-use**: $0.006 per second of generated audio
- **No monthly fees**: Only pay for what you use
- **Volume discounts**: Available for high usage
- **Free tier**: Limited usage for testing

## Next Steps

1. **Set up your API key** following this guide
2. **Create your first project**
3. **Upload a voice sample** and create your first voice
4. **Generate speech** and test the quality
5. **Experiment** with different voices and text

## All Three Services Running

You can run all three voice cloning services simultaneously:

- **Fish Audio**: http://localhost:3000
- **ElevenLabs**: http://localhost:3001  
- **Resemble.ai**: http://localhost:3002

Each service has its own strengths - try them all to see which works best for your needs!

Enjoy creating amazing AI voices with Resemble.ai! 🎉
